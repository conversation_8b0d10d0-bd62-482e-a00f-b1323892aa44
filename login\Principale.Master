﻿<%@ Master Language="C#" AutoEventWireup="true" CodeBehind="Principale.master.cs"
    Inherits="login.Principale" %>
<%--
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="fr">--%>

<!DOCTYPE html>
<html lang="en">
<head id="Head1" runat="server">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>Themis Admin</title>
	<link rel="icon" type="image/png" href="~/assets/images/favicon.ico2" />
     <link href="~/assets/js/multiSelect/chosen/chosen.css" rel="stylesheet" type="text/css" />
    <link href="~/assets/js/multiSelect/chosen/prism.css" rel="stylesheet" type="text/css" />
      <link href="~/assets/css/jquery.datetimepicker.css" rel="stylesheet" type="text/css" />
    <link href="~/assets/js/multiSelect/css/multi-select.css" rel="stylesheet" type="text/css" />
   

    <link href="~/assets/js/treeview/css/jquery-checktree.css" rel="stylesheet" type="text/css" />

    <!--basic styles-->
    <link href="~/assets/css/bootstrap.min.css" rel="stylesheet" />
    
 <%--   fonts--%>
    <link rel="stylesheet" href="~/assets/css/ace-fonts.css" />
    <!--ace styles-->
    <link rel="stylesheet" href="~/assets/css/ace.min.css" />
    <link rel="stylesheet" href="~/assets/css/ace-responsive.min.css" />
    <link rel="stylesheet" href="~/assets/css/ace-skins.min.css" />
    <link rel="stylesheet" href="~/assets/css/font-awesome.min.css" />    
    <link href="~/assets/css/jquery-ui-1.10.3.css" rel="stylesheet" type="text/css" />

    <link href="~/Styles/Site.css" rel="stylesheet" type="text/css" />



   <!-- Add jQuery library -->
    <script src="../assets/js/jquery-2.0.3.min.js" type="text/javascript"></script>
    <%--<script src="../assets/js/jquery-latest.min.js" type="text/javascript"></script>--%>
    <script src="../assets/js/jquery-ui-1.9.2.js" type="text/javascript"></script>
 
    <%--   <script src="../assets/js/bootstrap.min.js" type="text/javascript"></script>--%>
    <!--[if lte IE 8]>
	    <script src="../assets/js/excanvas.min.js"></script>
    <![endif]-->
   <%-- <script src="../assets/js/jquery-ui-1.10.3.custom.min.js" type="text/javascript"></script>--%>
    <script src="../assets/js/jquery.ui.touch-punch.min.js" type="text/javascript"></script>
    <script src="../assets/js/markdown/markdown.min.js" type="text/javascript"></script>
    <script src="../assets/js/markdown/bootstrap-markdown.min.js" type="text/javascript"></script>
    <script src="../assets/js/jquery.hotkeys.min.js" type="text/javascript"></script>
    <script src="../assets/js/bootstrap-wysiwyg.min.js" type="text/javascript"></script>
    <script src="../assets/js/jquery.slimscroll.min.js" type="text/javascript"></script>
    <script src="../assets/js/jquery.easy-pie-chart.min.js" type="text/javascript"></script>
    <script src="../assets/js/jquery.sparkline.min.js" type="text/javascript"></script>
    <script src="../assets/js/flot/jquery.flot.min.js" type="text/javascript"></script>
    <script src="../assets/js/flot/jquery.flot.pie.min.js" type="text/javascript"></script>
    <script src="../assets/js/flot/jquery.flot.resize.min.js" type="text/javascript"></script>
    <script src="../assets/js/bootbox.min.js" type="text/javascript"></script>
    <script src="../assets/js/multiSelect/jquery.multi-select.js" type="text/javascript"></script>
    <script src="../assets/js/treeview/jquery-checktree.js" type="text/javascript"></script>
    <script src="../assets/js/markdown/markdown.min.js" type="text/javascript"></script>
    <script src="../assets/js/markdown/bootstrap-markdown.min.js" type="text/javascript"></script>

    <script src="../assets/js/jquery.hotkeys.min.js" type="text/javascript"></script>
    <script src="../assets/js/bootstrap-wysiwyg.min.js" type="text/javascript"></script>
    <script src="../assets/js/jquery.confirm.js" type="text/javascript"></script>
    <%--<script src="/assets/js/bootstrap-window.js" type="text/javascript"></script>--%>

     <link href="~/assets/js/timepicker/jquery-ui-timepicker-addon.css" rel="stylesheet" type="text/css" />
     
    <!-- Add mousewheel plugin (this is optional) -->
    <script src="../assets/js/fancybox2/lib/jquery.mousewheel-3.0.6.pack.js" type="text/javascript"></script>

	<!-- Add fancyBox main JS and CSS files -->
	<%--<script type="text/javascript" src="../source/jquery.fancybox.js?v=2.1.5"></script>--%>
    <script src="../assets/js/fancybox2/source/jquery.fancybox.js" type="text/javascript"></script>
    <link href="~/assets/js/fancybox2/source/jquery.fancybox.css" media="screen" rel="stylesheet" type="text/css" />

    <script src="../assets/js/ace-elements.min.js" type="text/javascript"></script>

    <script src="../assets/js/multiSelect/chosen/chosen.jquery.js" type="text/javascript"></script>

   <%-- <link href="~/assets/js/pwstrength/bootstrap3.css" rel="stylesheet" type="text/css" />--%>
   <%-- <script src="../assets/js/pwstrength/bootstrap3.js" type="text/javascript"></script>--%>
    <script src="../assets/js/pwstrength/pwstrength.js" type="text/javascript"></script>
    <script src="../assets/js/pwstrength/zxcvbn-async.js" type="text/javascript"></script>

	<script src="../assets/js/bootstrap-strength-meter/bootstrap-strength-meter.js" type="text/javascript"></script>

    <script src="../assets/js/zclip/jquery.zeroclipboard.min.js" type="text/javascript"></script>

    <link href="Styles/BootstrapCustom.css" rel="stylesheet" type="text/css" />
    <script src="../assets/js/jquery.easyModal.js" type="text/javascript"></script>


     <%-- <script type="text/javascript" src="//cdn.jsdelivr.net/bootstrap/3.2.0/js/bootstrap.min.js"></script>--%>
    <script src="../assets/js/bootstrap.min.js" type="text/javascript"></script>

    <link href="assets/js/bootstrapvalidator/css/bootstrapValidator.css" rel="stylesheet" type="text/css" />
    <script src="../assets/js/bootstrapvalidator/js/bootstrapValidator.min.js" type="text/javascript"></script>
    <script src="../assets/js/bootstrapvalidator/moment/moment-with-locales.js" type="text/javascript"></script>

    <script src="../assets/js/progressbar.js" type="text/javascript"></script>
    <script src="../assets/js/browser/jquery.browser.js" type="text/javascript"></script>


    <link href="~/assets/css/prism/prism.css" rel="stylesheet" type="text/css" />
    <script src="../assets/js/prism/prism.js" type="text/javascript"></script>
    
    <script src="../assets/js/upload/js/jquery.fileupload.js" type="text/javascript"></script>
    <link href="~/assets/js/upload/css/jquery.fileupload.css" rel="stylesheet" type="text/css" />

    <script src="../assets/js/checkboxes/jquery.checkboxes-1.0.6.min.js" type="text/javascript"></script>

    <script src="../assets/js/price_format/jquery.price_format.2.0.js" type="text/javascript"></script>
	   <script src="../assets/js/xml2json/xml2json.js"></script>
    <script src="../assets/js/traduction_commune.js" type="text/javascript"></script>

    <asp:ContentPlaceHolder ID="HeadContent" runat="server">
    </asp:ContentPlaceHolder>

	<link href="~/assets/css/admin.css" rel="stylesheet" type="text/css" />
</head>
<body >
    <form id="form1" runat="server">

    <div id="dialog-confirm"></div>

    <!--body style class="skin-3"-->
    <input id="input_prefixname" type="hidden" value="abo" />
    <input id="input_pagename" type="hidden" value="abo_queue" />
    <div id="navbar" class="navbar navbar-default">
        
        <div class="navbar-container" id="navbar-container">
        
        <div class="navbar-header pull-left">
            <a href="#" class="navbar-brand"><i class="icon-leaf"></i> Thémis Admin - <%= System.Web.Configuration.WebConfigurationManager.AppSettings["Version"]%></a> 
        </div>
         <div id="StructureName" class="navbar-header"> <span data-trad="title_structure_name"></span></div>
         <!-- /.navbar-header left -->
  
         <div class="navbar-header pull-right">        
			 <ul class="nav ace-nav">
                <li class="grey">
                    <a data-toggle="dropdown" href="#" class="dropdown-toggle">
                        <!--img class="nav-user-photo" src="/admin/assets/avatars/user.jpg" alt="User's Photo" /-->
                        <i class="icon-user icon-large"></i>
                            <span class="user-info"><label data-trad="lbl_bonjour"></label> 
                                <div class="bold">


                                    <asp:Label ID="lblLoginName" runat="server"/>
                                </div> 
                             </span><i class="icon-caret-down">
                        </i>
                    </a>

                    <ul class="user-menu pull-right dropdown-menu dropdown-yellow dropdown-caret dropdown-closer">
                        <li><a href="/LoginStructure.aspx" runat="server" id="lnkStructures" visible="false"><i class="icon-mail-reply-all"></i>
                            <span data-trad="changer_structure"></span></a></li>

                        <li class="divider"></li>
                        <li><a href="#" id="lnkHelp"><i class="icon-info-sign"></i><label data-trad="help">Aide</label> </a></li>
                        <li class="divider"></li>

                        <!-- Boutons de langue -->
                        <li><a href="#" onclick="changeLanguage('fr'); return false;"><i class="icon-flag"></i> Français</a></li>
                        <li><a href="#" onclick="changeLanguage('de'); return false;"><i class="icon-flag"></i> Deutsch</a></li>
                        <li><a href="#" onclick="changeLanguage('en'); return false;"><i class="icon-flag"></i> English</a></li>

                        <li class="divider"></li>

                        <li>
                            <asp:LinkButton ID="lbDeconnexion" runat="server" OnClick="lbDeconnexion_click"><i class="icon-off"></i><asp:Label
                                ID="lblDisconnect" runat="server" Text="Déconnexion" data-trad="se_deconnecter"></asp:Label>
                            </asp:LinkButton>
                        </li>
                    </ul>
                </li>
            </ul>
            <!--/.ace-nav-->
        </div>
        <!-- /.navbar-header right -->
        </div>
        <!-- /.navbar-container -->
    </div>
    <!--- end navbar ---->

    <div id="pourFormatDatepicker" runat="server"></div>
     <input type="hidden" id="dateLang" />

    <div class="main-container" id="main-container">
    
        <div class="main-container-inner">
             <a class="menu-toggler" id="menu-toggler" href="#"><span class="menu-text"></span></a>
                <div id="poursidebar" runat="server">
                </div>
                <!--- end sidebar ---->

                <!-- contennu de la page-->
                <div class="main-content">
                    <!-- BREADCRUMBS & SEARCH BAR -->
                    <div class="breadcrumbs" id="breadcrumbs">
                        <ol class="breadcrumb">
                        </ol>
                    </div>
                    <!-- END BREADCRUMBS & SEARCH BAR -->
                    <!-- PAGE CONTENT -->
                    <div class="page-content">

                        <div class="row">
                            <div class="col-xs-12">
                               
								<!-- PAGE CONTENT BEGINS -->
                                <div id="loading"></div>
                                <div id="loadingmodal">
                                    <img src="../assets/images/loader.gif" alt="loader" />
                                    <span data-trad="loading"></span>
                                </div>

                                <div id="error">
                                    <asp:Label ID="lblMsgSuccess" CssClass="hidden" runat="server" ClientIDMode="Static"></asp:Label>
                                </div>
                                <asp:ContentPlaceHolder ID="MainContent" runat="server" />

								<!-- PAGE CONTENT ENDS -->
							</div>
                        
                        </div>

                    </div>
                    <!-- PAGE CONTENT ENDS -->
                </div>
        </div>
    </div>

    <a href="#" id="btn-scroll-up" class="btn-scroll-up btn btn-small btn-inverse"><i
        class="icon-double-angle-up icon-only bigger-110"></i></a>
    </form>


    <div class="modal fade" id="modalGeneric" tabindex="-1" role="dialog" aria-labelledby="modalGenericLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">

        </div>
    </div>
<!-- Modal 
<div class="modal fade" id="modalGeneric" tabindex="-1" role="dialog" >
  <div class="modal-dialog modal-xl" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
       
      </div>
      <div class="modal-body">
        
      </div>
      <div class="modal-footer">
       
      </div>
    </div>
  </div>
</div>
-->

    <!-- Modal -->
<div class="modal fade" id="modalMessage" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <%--<div class="modal-header">
        <h4 class="modal-title" id="myModalLabel"></h4>
      </div>--%>
      <div class="modal-body" >
            <i class="icon icon-spinner icon-spin"></i>
            <span data-trad="loading"></span>
      </div>
    </div>
  </div>
</div>



       <!-- Modal 
<div class="modal fade" id="modalMessage" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
        <h4 class="modal-title" id="myModalLabel"></h4>
      </div>
      <div class="modal-body">
        
      </div>
    </div>
  </div>
</div>-->


  <!-- Modal 
<div class="modal fade bs-example-modal-sm" id="myModal" tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-sm">
    <div class="modal-content">
      <i class="fa fa-spinner fa-spin"></i>
    </div>
  </div>
</div>
-->
  
      <!--ace scripts-->
    <script src="../assets/js/ace-elements.min.js" type="text/javascript"></script>
    <script src="../assets/js/ace.min.js" type="text/javascript"></script>
    <!--specification scripts-->
    <script src="../assets/js/bibliotheque_commune.js" type="text/javascript"></script>
    
     <script type="text/javascript">
var urlPreviewTemplate = '<%=ConfigurationSettings.AppSettings["FilePreviewTemplate"].ToString() %>';

// Fonction pour traduire un élément
function translateElement($element) {
    var tradKey = $element.attr('data-trad');
    if (tradKey && typeof ReadXmlTranslate === 'function') {
        var translation = ReadXmlTranslate(tradKey);
        if (translation && translation !== tradKey) {
            if ($element.is('input[type="button"], input[type="submit"], button')) {
                $element.val(translation);
            } else if (tradKey === 'lbl_bonjour') {
                // Pour lbl_bonjour, utiliser .html() pour interpréter les balises HTML
                $element.html(translation);
            } else {
                $element.text(translation);
            }
            return true;
        }
    }
    return false;
}

// Fonction pour changer la langue
function changeLanguage(lang) {


    // Utiliser ForceLanguage si disponible
    if (typeof ForceLanguage === 'function') {
        ForceLanguage(lang);
    } else {

        return;
    }

    // Sauvegarder la langue
    localStorage.setItem('selectedLanguage', lang);

    // Fonction de retraduction complète
    function performCompleteRetranslation() {


        // 1. Relancer LaunchTraduction pour tous les éléments data-trad
        if (typeof LaunchTraduction === 'function') {
            LaunchTraduction();
        }

        // 2. Forcer la retraduction de TOUS les éléments data-trad sur la page
        var translatedCount = 0;
        $('[data-trad]').each(function() {
            if (translateElement($(this))) {
                translatedCount++;
            }
        });

        // 3. Traduire spécifiquement les boutons générés dynamiquement
        $('.spdetailmanifseance span[data-trad], .spfulldetail span[data-trad]').each(function() {
            translateElement($(this));
        });

        // 4. Traduire les tableaux
        if (typeof TranslateAllTablesInContainer === 'function') {
            TranslateAllTablesInContainer('body');
        }

        // 5. Forcer la retraduction des éléments spécifiques après un court délai
        setTimeout(function() {
            // Menu de gauche
            $('.sidebar-menu [data-trad]').each(function() {
                translateElement($(this));
            });

            // Contenu principal
            $('.main-content [data-trad]').each(function() {
                translateElement($(this));
            });

            // Éléments dans les iframes ou contenus dynamiques
            $('iframe').each(function() {
                try {
                    var iframeDoc = $(this).contents();
                    iframeDoc.find('[data-trad]').each(function() {
                        translateElement($(this));
                    });
                } catch(e) {
                    // Ignorer les erreurs de cross-origin
                }
            });


        }, 100);
    }

    // Fonction de vérification que les traductions sont bien chargées
    function verifyTranslationsLoaded(targetLang, callback) {
        var testKey = 'menu_module_traductions';
        var maxAttempts = 10;
        var attempt = 0;

        function checkTranslation() {
            attempt++;


            if (typeof ReadXmlTranslate === 'function') {
                var translation = ReadXmlTranslate(testKey);


                // Vérifier si la traduction correspond à la langue cible
                var isCorrectLang = false;
                if (targetLang === 'fr' && translation === 'Traductions des modules') isCorrectLang = true;
                if (targetLang === 'de' && translation === 'Modulübersetzungen') isCorrectLang = true;
                if (targetLang === 'en' && translation === 'Module translations') isCorrectLang = true;

                if (isCorrectLang) {

                    callback();
                    return;
                }
            }

            if (attempt < maxAttempts) {
                setTimeout(checkTranslation, 200);
            } else {

                callback();
            }
        }

        checkTranslation();
    }

    // Délai initial adapté selon la langue (anglais et allemand semblent plus lents)
    var initialDelay = (lang === 'en' || lang === 'de') ? 800 : 500;

    // Attendre que les traductions soient chargées puis relancer TOUT
    setTimeout(function() {
        verifyTranslationsLoaded(lang, function() {
            performCompleteRetranslation();

            // Double vérification après un délai plus long pour l'anglais et l'allemand
            var secondDelay = (lang === 'en' || lang === 'de') ? 600 : 300;
            setTimeout(function() {
                performCompleteRetranslation();
            }, secondDelay);

            // Triple vérification spéciale pour l'anglais et l'allemand
            if (lang === 'en' || lang === 'de') {
                setTimeout(function() {

                    performCompleteRetranslation();
                }, 1000);
            }
        });

        // 6. Afficher une notification
        var langNames = {
            'fr': 'Français',
            'de': 'Deutsch',
            'en': 'English'
        };

        var notification = $('<div class="alert alert-success" style="position: fixed; top: 70px; right: 20px; z-index: 9999; min-width: 250px;">' +
            '<i class="icon-ok"></i> Langue changée vers: ' + langNames[lang] +
            '</div>');

        $('body').append(notification);

        setTimeout(function() {
            notification.fadeOut(500, function() {
                $(this).remove();
            });
        }, 2500);

    }, initialDelay);
}

$(document).ready(function() {
    // Charger la langue sauvegardée au démarrage
    var savedLang = localStorage.getItem('selectedLanguage');
    if (savedLang && savedLang !== 'fr') {

        setTimeout(function() {
            changeLanguage(savedLang);
        }, 1500); // Attendre que tout soit chargé
    } else {
        LaunchTraduction();
    }
})
$(window).load(function() {




    // do something 
    $('body').attr('structureCurrentId', getCookie('structureId'));
    activLi();

    openMenuSection();

    if (localStorage.getObj('structureObj') != null) {
        var structId = localStorage.getObj('structureObj').structureId;
        var structName = localStorage.getObj('structureObj').structureName;

        $('#StructureName').html(ReadXmlTranslate("title_structure_name").replace('[structureName]', structName).replace('[structureId]', structId));
    }


    /*if (localStorage.getItem('structureObj') != null) {
        var structureSelected = localStorage.getItem('structureObj');
        var structureSelectedObj = JSON.parse(structureSelected);

        $('#StructureName').html(ReadXmlTranslate("title_structure_name").replace('[structureName]', structureSelectedObj.structureName).replace('[structureId]', structureSelectedObj.structureId));
    }*/




});
</script>
     
     <asp:ContentPlaceHolder ID="FooterContent" runat="server">
    </asp:ContentPlaceHolder>

</body>
</html>
