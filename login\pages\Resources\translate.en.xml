<?xml version="1.0" encoding="utf-8"?>
 <Root>
	<Generals>
		<help trad="help">Help</help>
		<quelle_structure trad="quelle_structure">Which Structure</quelle_structure>
		<remember_me trad="remember_me">Remember me</remember_me>
		<title_identification trad="title_identification">identification</title_identification>
		<head_login_view trad="head_login_view">Welcome [login]</head_login_view>
		<lbl_bonjour trad="lbl_bonjour">&lt;small&gt;Hello &lt;br/&gt; &lt;/small&gt;</lbl_bonjour>
		<title_structure_name trad="title_structure_name"> | Structure [structureName] - ([structureId]) </title_structure_name>
		<changer_structure trad="changer_structure">Change structure</changer_structure>
		<changer_langue trad="changer_langue">Change language</changer_langue>
		<loading trad="loading">Please wait, processing is in progress</loading>
		<config_key_not_found trad="config_key_not_found">The key [key] is not found in web.config</config_key_not_found>
		<structure_id trad="structure_id">structure id</structure_id>
		<structure_name trad="structure_name">structure name</structure_name>
		<se_connecter trad="se_connecter">log in</se_connecter>
		<se_deconnecter trad="se_deconnecter">log out</se_deconnecter>
		<activer trad="activer">activate</activer>
		<desactiver trad="desactiver">deactivate</desactiver>
		<valider trad="valider">Validate</valider>
		<annuler trad="annuler">Cancel</annuler>
		<oui trad="oui">Yes</oui>
		<non trad="non">no</non>
		<erreur trad="erreur">error</erreur>
		<export_pdf trad="export_pdf">Export to PDF</export_pdf>
		<export_xls trad="export_xls">Export to Excel</export_xls>
		<export_print trad="export_print">Print</export_print>
		<modifier trad="modifier">Edit</modifier>
		<ajouter trad="ajouter">Add</ajouter>
		<aucune_reserve trad="aucune_reserve">No reserve</aucune_reserve>
		<aucune_maquette trad="aucune_maquette">No template</aucune_maquette>
		<title_modal_error trad="title_modal_error">Error</title_modal_error>
		<aucun trad="aucun">None</aucun>
		<enregistrer trad="enregistrer">save</enregistrer>
		<svg_file_not_found trad="svg_file_not_found">SVG folder not found</svg_file_not_found>
		<svg_file_not_exist trad="svg_file_not_exist">SVG folder does not exist</svg_file_not_exist>
		<file_not_found trad="file_not_found">File [filename] does not exist</file_not_found>
		<aucune_structure_selectionnee trad="aucune_structure_selectionnee">No Structure Selected</aucune_structure_selectionnee>
		<play_job trad="play_job">Force publication</play_job>
		<structures trad="structures">structures</structures>
		<date_debut trad="date_debut">Start date</date_debut>
		<date_fin trad="date_fin">End date</date_fin>
		<previous trad="previous">Previous</previous>
		<next trad="next">Next</next>
		<title_debut trad="title_debut">Start</title_debut>
		<title_fin trad="title_fin">End</title_fin>
		<title_nb_places trad="title_nb_places">Number of seats</title_nb_places>
		<title_nb_places_min trad="title_nb_places_min">Minimum number of seats</title_nb_places_min>
		<title_nb_places_max trad="title_nb_places_max">Maximum number of seats</title_nb_places_max>
		<title_options trad="title_options">Options</title_options>
		<title_active_resume trad="title_active_resume">Activate summary</title_active_resume>
		<title_choix_dates trad="title_choix_dates">Date selection</title_choix_dates>
		<title_choix_etats trad="title_choix_etats">Status selection</title_choix_etats>
		<etat_reserver trad="etat_reserver">reserved</etat_reserver>
		<etat_payer trad="etat_payer">Paid</etat_payer>
		<etat_editer trad="etat_editer">Edited</etat_editer>
		<select_shift trad="select_shift">You can select multiple sessions by holding down the SHIFT key</select_shift>
		<select_all_manifestations trad="select_all_manifestations">Select all events</select_all_manifestations>
		<select_all_tarifs trad="select_all_tarifs">Select all rates</select_all_tarifs>
		<msg_box_error_title trad="msg_box_error_title">Error</msg_box_error_title>
		<!--commentaires-->
		<panier_identification trad="panier_identification">Cart and identification</panier_identification>
		<seances_liste trad="seances_liste">List of sessions</seances_liste>
		<pop_identification trad="pop_identification">identification popup</pop_identification>
		<attente_paiement trad="attente_paiement">Waiting for payment</attente_paiement>
		<info_paiement trad="info_paiement">payment comment info</info_paiement>
		<annuler_paiement trad="annuler_paiement">Cancel payment</annuler_paiement>
		<error_paiement trad="error_paiement">Payment error</error_paiement>
		<detail_commande trad="detail_commande">Order detail</detail_commande>
		<choix_seance trad="choix_seance">session selection</choix_seance>
		<choix_manifs trad="choix_manifs">event selection</choix_manifs>
		<choix_categs trad="choix_categs">category selection</choix_categs>
		<choix_formule trad="choix_formule">formula selection</choix_formule>
		<creation_abo trad="creation_abo">subscription creation</creation_abo>
		<modif_tarif trad="modif_tarif">edit rate</modif_tarif>
		<validation_abo trad="validation_abo">subscription validation</validation_abo>
		<paiement trad="paiement">payment</paiement>
		<connexion trad="connexion">connection</connexion>
		<creation_profil trad="creation_profil">profile creation</creation_profil>
		<confirmation_profil trad="confirmation_profil">profile confirmation</confirmation_profil>
		<update_profil trad="update_profil">profile update</update_profil>
		<commentaire_formule trad="commentaire_formule">Comment by formula/group</commentaire_formule>
		<aucun_commentaire trad="aucun_commentaire">No comment defined</aucun_commentaire>
		<commentaire_bas trad="commentaire_bas">bottom comment</commentaire_bas>
		<commentaire_haut trad="commentaire_haut">top comment</commentaire_haut>
		<recup_last_file trad="recup_last_file">Retrieve last saved file</recup_last_file>
		<select_file trad="select_file">Select a file</select_file>
		<preview_file trad="preview_file">Preview rendering</preview_file>
		<info_panier trad="info_panier">cart info</info_panier>
		<info_login trad="info_login">login info</info_login>
		<info_connexion trad="info_connexion">connection info</info_connexion>
		<creation_compte trad="creation_compte">account creation</creation_compte>
		<confirm_update_compte trad="confirm_update_compte">confirm account update</confirm_update_compte>
		<no_langage_in_commentaire trad="no_langage_in_commentaire">No language</no_langage_in_commentaire>
		<confirm_msg_recup_last_file trad="confirm_msg_recup_last_file">Are you sure you want to retrieve the last file? This action will result in loss of current work.</confirm_msg_recup_last_file>
		<!-- le select des langages dans les commentaires-->
		<!--fin commentaires-->
		<!--Tooltips-->
		<tooltip_update_gestion_place_parent trad="tooltip_update_gestion_place_parent"> Update a parent sales rule</tooltip_update_gestion_place_parent>
		<tooltip_add_gestion_place trad="tooltip_add_gestion_place">Add a sales rule</tooltip_add_gestion_place>
		<tooltip_delete_gestion_place trad="tooltip_delete_gestion_place">Delete a sales rule</tooltip_delete_gestion_place>
		<tooltip_copy_gestion_place trad="tooltip_copy_gestion_place">Copy a sales rule</tooltip_copy_gestion_place>
		<tooltip_update_gestion_place trad="tooltip_update_gestion_place">Update a sales rule</tooltip_update_gestion_place>
		<tooltip_update_profil_acheteur trad="tooltip_update_profil_acheteur">Update buyer profile</tooltip_update_profil_acheteur>
		<tooltip_delete_profil_acheteur trad="tooltip_delete_profil_acheteur">Delete buyer profile</tooltip_delete_profil_acheteur>
		<tooltip_update_offre trad="tooltip_update_offre">Update offer</tooltip_update_offre>
		<!-- fin Tooltips-->
		<manifestation_id trad="manifestation_id">event id</manifestation_id>
		<manifestation_nom trad="manifestation_nom">event name</manifestation_nom>
		<manifestation_groupe trad="manifestation_groupe">event group</manifestation_groupe>
		<lieu trad="lieu">location</lieu>
		<status trad="status">status</status>
		<ajout_regles_seances_selectionnees trad="ajout_regles_seances_selectionnees">Add rules to all selected sessions</ajout_regles_seances_selectionnees>
		<supprimer_regles_seances_selectionnees trad="supprimer_regles_seances_selectionnees">Remove rules from all selected sessions</supprimer_regles_seances_selectionnees>
		<seance_id trad="seance_id">session id</seance_id>
		<seance_date_heure trad="seance_date_heure">session</seance_date_heure>
		<actions trad="actions">actions</actions>
		<mode_prise_place_sur_plan trad="mode_prise_place_sur_plan"> On plan</mode_prise_place_sur_plan>
		<mode_prise_place_automatique trad="mode_prise_place_automatique"> Automatic</mode_prise_place_automatique>
		<mode_prise_place trad="mode_prise_place">Seat selection mode</mode_prise_place>
		<type_acces trad="type_acces">Access type</type_acces>
		<sans_billet trad="sans_billet">no ticket</sans_billet>
		<global_au_panier trad="global_au_panier">Global to cart</global_au_panier>
		<type_prise_place trad="type_prise_place">Seat selection type</type_prise_place>
		<voir_place trad="voir_place"> View seats</voir_place>
		<placement_libre trad="placement_libre"> Free seating</placement_libre>
		<meme_place trad="meme_place"> Same seat</meme_place>
		<jours trad="jours">days</jours>
		<heures trad="heures">hours</heures>
		<gestion_place_id trad="gestion_place_id">seat management id</gestion_place_id>
		<date_operation trad="date_operation">operation date</date_operation>
		<date_deb_validite trad="date_deb_validite">from (before)</date_deb_validite>
		<date_fin_validite trad="date_fin_validite">until (before)</date_fin_validite>
		<date_deb_validite_int trad="date_deb_validite_int">from (before)</date_deb_validite_int>
		<date_fin_validite_int trad="date_fin_validite_int">until (before)</date_fin_validite_int>
		<categorie trad="categorie">category</categorie>
		<nb_place_min trad="nb_place_min">min</nb_place_min>
		<nb_place_max trad="nb_place_max">max</nb_place_max>
		<type_tarif trad="type_tarif">rate</type_tarif>
		<reserves trad="reserves">reserves</reserves>
		<type_envoi trad="type_envoi">Delivery method</type_envoi>
		<nb_place_dispo trad="nb_place_dispo">available</nb_place_dispo>
		<nom_formule trad="nom_formule">formula name</nom_formule>
		<est_valide trad="est_valide">is valid</est_valide>
		<manifestation_liste trad="manifestation_liste">list of events</manifestation_liste>
		<manifestation_liste_formule trad="manifestation_liste_formule">list of events for the formula</manifestation_liste_formule>
		<title_regle_generale trad="title_regle_generale">list for general rule <![CDATA[<b>[sessionName] </b>]]></title_regle_generale>
		<title_regle_fille trad="title_regle_fille">list of child rules</title_regle_fille>
		<status_toutes_seances trad="status_toutes_seances">All sessions on sale</status_toutes_seances>
		<status_aucune_seances trad="status_aucune_seances">No sessions on sale</status_aucune_seances>
		<status_quelques_seances trad="status_quelques_seances">Some sessions on sale</status_quelques_seances>
		<profil trad="profil">profile</profil>
		<new_user trad="new_user">New user</new_user>
		<login trad="login">login</login>
		<email trad="email">email</email>
		<password trad="password">password </password>
		<confirm_password trad="confirm_password">confirm password</confirm_password>
		<password_different trad="password_different">Passwords are different</password_different>
		<mettre_a_jour trad="mettre_a_jour">Update</mettre_a_jour>
		<mail_non_valide trad="mail_non_valide">Email address is not valid</mail_non_valide>
		<aucun_resultat_trouve trad="aucun_resultat_trouve">No results found!</aucun_resultat_trouve>
		<!--messages success ou error-->
		<msg_error_new_password trad="msg_error_new_password">An email will be sent to you to change your password.</msg_error_new_password>
		<msg_error_update_db trad="msg_error_update_db">An error occurred while updating this file [fileName]</msg_error_update_db>
		<msg_error_identification trad="msg_error_identification">Access denied: input error</msg_error_identification>
		<msg_error_identification_existe trad="msg_error_identification_existe">User already exists</msg_error_identification_existe>
		<success_commentaire_insert trad="success_commentaire_insert">Comment added successfully</success_commentaire_insert>
		<success_css_insert trad="success_css_insert">Comment added successfully</success_css_insert>
		<msg_default_file_css trad="msg_default_file_css">Warning !!! default file has been loaded </msg_default_file_css>
		<not_svg_default_file trad="not_svg_default_file">Warning !!! No file found in </not_svg_default_file>
		<msg_error_load_user_list trad="msg_error_load_user_list">No users in the database</msg_error_load_user_list>
		<msg_success_delete_user trad="msg_success_delete_user">User deleted successfully</msg_success_delete_user>
		<msg_error_delete_user trad="msg_error_delete_user">An error occurred while deleting the user</msg_error_delete_user>
		<msg_success_insert_user trad="msg_success_insert_user">User created successfully</msg_success_insert_user>
		<msg_error_insert_user trad="msg_error_insert_user">An error occurred while adding the user</msg_error_insert_user>
		<msg_error_password_user trad="msg_error_password_user">Password is empty or does not match</msg_error_password_user>
		<msg_error_delete_structure_user trad="msg_error_delete_structure_user">An error occurred while deleting the user's structure</msg_error_delete_structure_user>
		<msg_success_update_user trad="msg_success_update_user">User saved successfully</msg_success_update_user>
		<msg_error_exist_user trad="msg_error_exist_user">User already exists</msg_error_exist_user>
		<msg_success_delete_gestion_place trad="msg_success_delete_gestion_place">Sales rule for session deleted successfully</msg_success_delete_gestion_place>
		<msg_error_delete_gestion_place trad="msg_error_delete_gestion_place">Error while deleting a rule</msg_error_delete_gestion_place>
		<msg_success_delete_all_gestion_place trad="msg_success_delete_all_gestion_place">Sales rules for sessions deleted successfully</msg_success_delete_all_gestion_place>
		<msg_error_delete_all_gestion_place trad="msg_error_delete_all_gestion_place">msg_error_delete_all_gestion_place</msg_error_delete_all_gestion_place>
		<msg_error_plusieurs_gestion_place trad="msg_error_plusieurs_gestion_place">Multiple parent sales rules found, causing an error</msg_error_plusieurs_gestion_place>
		<msg_success_update_gestion_place trad="msg_success_update_gestion_place">Rule edit completed successfully </msg_success_update_gestion_place>
		<msg_error_update_gestion_place trad="msg_error_update_gestion_place">An error occurred while updating the sales rule</msg_error_update_gestion_place>
		<msg_error_insert_gestion_place trad="msg_error_insert_gestion_place">An error occurred while saving the sales rule</msg_error_insert_gestion_place>
		<msg_success_insert_gestion_place trad="msg_success_insert_gestion_place">Rule saved successfully</msg_success_insert_gestion_place>
		<msg_success_insert_all_gestion_place trad="msg_success_insert_all_gestion_place">All rules saved successfully</msg_success_insert_all_gestion_place>
		<msg_success_copy_gestion_place trad="msg_success_copy_gestion_place">Rules copied successfully</msg_success_copy_gestion_place>
		<msg_error_copy_gestion_place trad="msg_error_copy_gestion_place">A problem occurred while copying sales rules</msg_error_copy_gestion_place>
		<msg_success_update_offre trad="msg_success_update_offre">Offer updated successfully</msg_success_update_offre>
		<msg_success_insert_gestion_place_formule trad="msg_success_insert_gestion_place_formule">Sales rule saved successfully for formula [formuleAboId]</msg_success_insert_gestion_place_formule>
		<msg_error_insert_gestion_place_formule trad="msg_error_insert_gestion_place_formule">An error occurred while saving the sales rule for formula [formuleAboId]</msg_error_insert_gestion_place_formule>
		<msg_success_update_gestion_place_formule trad="msg_success_update_gestion_place_formule">Sales rule saved successfully for formula [formuleAboId]</msg_success_update_gestion_place_formule>
		<msg_error_update_gestion_place_formule trad="msg_error_update_gestion_place_formule">An error occurred while updating the sales rule for formula [formuleAboId]</msg_error_update_gestion_place_formule>
		<msg_success_delete_gestion_place_formule trad="msg_success_delete_gestion_place_formule">Sales rule deleted successfully for formula [formuleAboId]</msg_success_delete_gestion_place_formule>
		<msg_error_delete_gestion_place_formule trad="msg_error_delete_gestion_place_formule">An error occurred while deleting seat management ID for formula [formuleAboId]</msg_error_delete_gestion_place_formule>
		<msg_success_delete_seance_formule trad="msg_success_delete_seance_formule">Sales rule deleted successfully for session</msg_success_delete_seance_formule>
		<msg_error_delete_seance_formule trad="msg_error_delete_seance_formule">An error occurred while deleting the session for formula [seanceId]</msg_error_delete_seance_formule>
		<success_tags_insert trad="success_tags_insert">Tag added successfully</success_tags_insert>
		<title_tag trad="title_tag">Tags</title_tag>
		<!-- fin messages success ou error-->
		<!-- confirm -->
		<confirm_msg_title trad="confirm_msg_title">Confirmation</confirm_msg_title>
		<confirm_msg_delete_all_gestion_place trad="confirm_msg_delete_all_gestion_place">Are you sure you want to delete all sales rules for this session?</confirm_msg_delete_all_gestion_place>
		<confirm_msg_delete_gestion_place trad="confirm_msg_delete_gestion_place">Are you sure you want to delete the sales rule for this session?</confirm_msg_delete_gestion_place>
		<confirm_msg_delete_user trad="confirm_msg_delete_user">Are you sure you want to delete this user?</confirm_msg_delete_user>
		<confirm_msg_delete_all_gestion_place_formule trad="confirm_msg_delete_all_gestion_place_formule">Are you sure you want to delete all sales rules for this formula?</confirm_msg_delete_all_gestion_place_formule>
		<confirm_msg_delete_formule trad="confirm_msg_delete_formule">Are you sure you want to delete this formula?</confirm_msg_delete_formule>
		<!-- fin confirm -->
		<!--message formulaire utilisateur-->
		<msg_error_login_empty trad="msg_error_login_empty">Login is required</msg_error_login_empty>
		<msg_error_email_empty trad="msg_error_email_empty">Email address is required</msg_error_email_empty>
		<msg_error_password_empty trad="msg_error_password_empty">Password is required</msg_error_password_empty>
		<msg_error_email_non_valide trad="msg_error_email_non_valide">Email address is not valid</msg_error_email_non_valide>
		<msg_error_password_same_login trad="msg_error_password_same_login">Password must be different from login</msg_error_password_same_login>
		<msg_error_confirm_password trad="msg_error_confirm_password">Password confirmation must match password</msg_error_confirm_password>
		<msg_error_password_stronger trad="msg_error_password_stronger">1 lowercase, 1 uppercase, 1 special character (! . ? @ # - _)</msg_error_password_stronger>
		<msg_error_min_password trad="msg_error_min_password">8 to 15 characters  </msg_error_min_password>
		<msg_error_date_deb_empty trad="msg_error_date_deb_empty">Start date is required</msg_error_date_deb_empty>
		<msg_error_date_fin_empty trad="msg_error_date_fin_empty">End date is required</msg_error_date_fin_empty>
		<msg_error_sql_empty trad="msg_error_sql_empty">SQL is required</msg_error_sql_empty>
		<msg_error_date_deb_not_valid trad="msg_error_date_deb_not_valid">Start date is not valid</msg_error_date_deb_not_valid>
		<msg_error_date_fin_not_valid trad="msg_error_date_fin_not_valid">End date is not valid</msg_error_date_fin_not_valid>
		<!-- fin message formulaire utilisateur-->
		<confirm_delete_gestion_place trad="confirm_delete_gestion_place">Are you sure you want to delete this sales rule?</confirm_delete_gestion_place>
		<confirm_delete_profil_acheteur trad="confirm_delete_profil_acheteur">Are you sure you want to delete this buyer profile?</confirm_delete_profil_acheteur>
		<user_exist trad="user_exist">User already exists</user_exist>
		<profil_acheteur_nom trad="profil_acheteur_nom">Buyer profile name</profil_acheteur_nom>
		<!--<msg_error_modal_produit trad="msg_error_modal_produit">Select at least one product</msg_error_modal_produit>
    <msg_error_modal_seance trad="msg_error_modal_seance">Select at least one session</msg_error_modal_seance>
    <msg_error_modal_manifestation trad="msg_error_modal_manifestation">Select at least one event</msg_error_modal_manifestation>
    <msg_error_modal_type_envoi trad="msg_error_modal_type_envoi">Select at least one delivery type</msg_error_modal_type_envoi>-->
		<msg_error_select_seances trad="msg_error_select_seances">Select at least one session</msg_error_select_seances>
		<msg_error_select_produit trad="msg_error_select_produit">Select at least one product</msg_error_select_produit>
		<msg_error_select_manifestation trad="msg_error_select_manifestation">Select at least one event</msg_error_select_manifestation>
		<msg_error_select_tarif trad="msg_error_select_tarif">Select at least one rate for a category</msg_error_select_tarif>
		<msg_error_select_reserve trad="msg_error_select_reserve">Select at least one reserve</msg_error_select_reserve>
		<msg_error_select_type_envoi trad="msg_error_select_type_envoi">Select at least one delivery type</msg_error_select_type_envoi>
		<msg_error_select_max_inferieur_min trad="modal_select_min_max">Maximum number is less than minimum number</msg_error_select_max_inferieur_min>
		<msg_error_select_maquette trad="modal_select_maquette">Select a template for [type_envoi_name]</msg_error_select_maquette>
		<msg_error_select_max trad="msg_error_select_max">Maximum number is required</msg_error_select_max>
		<msg_error_select_min trad="msg_error_select_min">Minimum number is required</msg_error_select_min>
		<msg_error_select_prise_place trad="msg_error_select_prise_place">Select a seat selection type: Automatic and/or On plan</msg_error_select_prise_place>
		<msg_error_date_debut_checked trad="msg_error_date_debut_checked">Select a start date</msg_error_date_debut_checked>
		<msg_error_date_fin_checked trad="msg_error_date_fin_checked">Select an end date</msg_error_date_fin_checked>
		<type_tarif_id trad="type_tarif_id">Rate ID</type_tarif_id>
		<type_tarif_nom trad="type_tarif_nom">Rate name</type_tarif_nom>
		<add_new_gestion_place_in_formule trad="add_new_gestion_place_in_formule">Create a sales rule for sessions for formula [formuleName]</add_new_gestion_place_in_formule>
		<edit_gestion_place_in_formule trad="edit_gestion_place_in_formule">Update event: [manifName] for subscription [formulenamecurrent]</edit_gestion_place_in_formule>
		<title_seances trad="title_seances">Session selection</title_seances>
		<add_new_product trad="add_new_product">Add for product [productName]</add_new_product>
		<title_edit_product trad="title_edit_product">Update product [productName]</title_edit_product>
		<title_copy_product trad="title_copy_product">Copy product [productName]</title_copy_product>
		<title_select_product trad="title_select_product">Select product(s)</title_select_product>
		<select_all_products trad="select_all_products">Select all products</select_all_products>
		<transaction_id trad="transaction_id">transaction id</transaction_id>
		<customer_number trad="customer_number">Customer No.</customer_number>
		<command_number trad="order_id">Order No.</command_number>
		<!--numéro de commande-->
		<basket_state trad="basket_state">Cart Status</basket_state>
		<detail trad="detail">Detail</detail>
		<detail_complet trad="detail_complet">Complete Detail</detail_complet>
		<detail_cash_manif_seance trad="detail_cash_manif_seance">Detail</detail_cash_manif_seance>
		<btn_full_detail trad="btn_full_detail">Complete Detail</btn_full_detail>
		<reference_unique_billet trad="reference_unique_billet">Unique ticket reference</reference_unique_billet>
		<dernier_etat_place trad="dernier_etat_place">Last seat status for the period</dernier_etat_place>
		<id_filiere_vente trad="id_filiere_vente">Sales channel ID</id_filiere_vente>
		<nom_filiere_vente trad="nom_filiere_vente">Sales channel name for ticket</nom_filiere_vente>
		<id_manifestation trad="id_manifestation">Event ID</id_manifestation>
		<nom_manifestation trad="nom_manifestation">Event name</nom_manifestation>
		<groupe_manifestations trad="groupe_manifestations">Event group</groupe_manifestations>
		<genre_manifestation trad="genre_manifestation">Event genre</genre_manifestation>
		<sous_genre_manifestation trad="sous_genre_manifestation">Event sub-genre</sous_genre_manifestation>
		<cible_manifestation trad="cible_manifestation">Event target audience</cible_manifestation>
		<id_seance trad="id_seance">Session ID</id_seance>
		<date_debut_seance trad="date_debut_seance">Session start date</date_debut_seance>
		<id_categorie trad="id_categorie">Category ID</id_categorie>
		<nom_categorie trad="nom_categorie">Category name</nom_categorie>
		<id_tarif trad="id_tarif">Rate ID</id_tarif>
		<nom_tarif trad="nom_tarif">Rate name</nom_tarif>
		<numero_commande trad="numero_commande">Order number</numero_commande>
		<mode_paiement trad="mode_paiement">Payment method</mode_paiement>
		<date_operation trad="date_operation">Operation date</date_operation>
		<id_identite trad="id_identite">Identity ID</id_identite>
		<nom_identite trad="nom_identite">Identity name</nom_identite>
		<prenom_identite trad="prenom_identite">Identity first name</prenom_identite>
		<civilite trad="civilite">Title</civilite>
		<code_postal trad="code_postal">Postal code</code_postal>
		<ville trad="ville">City</ville>
		<pays trad="pays">Country</pays>
		<date_naissance trad="date_naissance">Date of birth</date_naissance>
		<filiere_creation_identite trad="filiere_creation_identite">Identity creation channel</filiere_creation_identite>
		<telephone_mobile trad="telephone_mobile">Mobile phone</telephone_mobile>
		<adresse_postale trad="adresse_postale">Postal address</adresse_postale>
		<opt_in trad="opt_in">OPT-IN</opt_in>
		<numero_billet trad="numero_billet">Ticket number</numero_billet>
		<sumSeance trad="sumSeance">Sum of sessions</sumSeance>
		<montant1 trad="montant1">Amount 1</montant1>
		<identite_id trad="identite_id">identity id</identite_id>
		<basket_id trad="basket_id">cart Id</basket_id>
		<certificate trad="certificate">Certificate</certificate>
		<card_number trad="card_number">Card No.</card_number>
		<card_type trad="card_type">Card No.</card_type>
		<paiement_date trad="paiement_date">Payment date</paiement_date>
		<print_home trad="print_home">print@home</print_home>
		<numero_billets trad="numero_billets">Ticket No.</numero_billets>
		<!-- champs 'as pdf'-->
		<etat_tous trad="etat_tous">all</etat_tous>
		<etat_invalide trad="etat_invalide">invalid</etat_invalide>
		<etat_cree trad="etat_cree">created</etat_cree>
		<entrees_validees_number trad="entrees_validees_number">Number of tickets sold</entrees_validees_number>
		<montant_vente_unitaire trad="montant_vente_unitaire">Unit sale amount</montant_vente_unitaire>
		<nbre_valider_vente_unitaire trad="nbre_valider_vente_unitaire">Nbr validated unit sale baskets</nbre_valider_vente_unitaire>
		<nbre_panier_creer trad="nbre_panier_creer">Nbr baskets created</nbre_panier_creer>
		<nombre_entrees_vente_abonnement trad="nombre_entrees_vente_abonnement">Nbr subscription tickets sold</nombre_entrees_vente_abonnement>
		<nombre_panier_valider_abonnement trad="nombre_panier_valider_abonnement">Nbr validated subscription baskets</nombre_panier_valider_abonnement>
		<montant_entree_abo trad="montant_entree_abo">Subscription ticket amount</montant_entree_abo>
		<nombre_panier_creer_abonnement trad="nombre_panier_creer_abonnement">Nbr subscription baskets created</nombre_panier_creer_abonnement>
		<somme_entree_valider_unit_abo trad="somme_entree_valider_unit_abo">Nbr validated unit/subscription entries (sum)</somme_entree_valider_unit_abo>
		<nombre_frais_envoi_valider trad="nombre_frais_envoi_valider">Nbr validated shipping fees</nombre_frais_envoi_valider>
		<nombre_produits_valider trad="nombre_produits_valider">Nbr validated products</nombre_produits_valider>
		<somme_frais_envoi trad="somme_frais_envoi">Shipping fees amount (sum)</somme_frais_envoi>
		<montant_produit trad="montant_produit">Product amount</montant_produit>
		<montant_total trad="montant_total">Total amount</montant_total>
		<montant_panier_formule_tarif trad="montant_panier_formule_tarif">Multi-subscription amount</montant_panier_formule_tarif>
		<frais_panier_formule_tarif trad="frais_panier_formule_tarif">Multi-subscription fees amount</frais_panier_formule_tarif>
		<nombre_payes_non_valides trad="nombre_payes_non_valides">Nbr paid not validated</nombre_payes_non_valides>
		<nbr_total_entrees_final trad="nbr_total_entrees_final">total nbr entries final</nbr_total_entrees_final>
		<sum_totale_final trad="sum_totale_final">total sum final</sum_totale_final>
		<resume_select_previous trad="resume_select_previous">Summary of previous selections: </resume_select_previous>
		<replace_sessions trad="replace_sessions">Replace sessions </replace_sessions>
		<label_source trad="label_source">Source</label_source>
		<label_destination trad="label_destination">Destination</label_destination>
		<date_le trad="date_le">session date </date_le>
		<seances trad="seances">sessions </seances>
		<resume_select_manifestations trad="resume_select_manifestations">Selected events: </resume_select_manifestations>
		<resume_select_tarifs trad="resume_select_tarifs">Selected rates: </resume_select_tarifs>
		<resume_select_date trad="resume_select_date">Selected dates: </resume_select_date>
		<resume_select_date trad="select_date">From [startDate] to [endDate] </resume_select_date>
		<aucun_filtre trad="aucun_filtre">No filter</aucun_filtre>
		<structure_a_exclure trad="structure_a_exclure">Structure to exclude</structure_a_exclure>
		<structure_a_inclure trad="structure_a_inclure">Structure to include</structure_a_inclure>
		<msg_error_aucun_droits_dans_bdd trad="msg_error_aucun_droits_dans_bdd">No rights in database</msg_error_aucun_droits_dans_bdd>
		<msg_success_update_droits trad="msg_success_update_droits">Rights successfully assigned to group </msg_success_update_droits>
		<msg_error_update_droits trad="msg_error_update_droits">An error occurred while updating rights</msg_error_update_droits>
		<CldarSH trad="CldarSH">Session selection by calendar</CldarSH>
		<MaxBlockSz trad="MaxBlockSz">Hide floor selection</MaxBlockSz>
		<ZapperEtag trad="ZapperEtag">If 1, skip floor zone</ZapperEtag>
		<ZapperZES trad="ZapperZES">If 1, skip floor zone section</ZapperZES>
		<msg_success_update_properties trad="msg_success_update_properties">Properties update completed successfully </msg_success_update_properties>
		<msg_error_update_properties trad="msg_error_update_properties">An error occurred while updating properties </msg_error_update_properties>
		<title_creer_connexion_client trad="title_creer_connexion_client">Create Client Connection</title_creer_connexion_client>
		<ip_serveur trad="ip_serveur">Server IP</ip_serveur>
		<bdd_name trad="bdd_name">Database name</bdd_name>
		<type_connexion_prod trad="type_connexion_prod">PROD</type_connexion_prod>
		<type_connexion_test trad="type_connexion_test">TEST</type_connexion_test>
		<msg_success_new_connexion trad="msg_success_new_connexion">Connection added successfully</msg_success_new_connexion>
		<msg_error_new_connexion trad="msg_error_new_connexion">An error occurred while saving the connection</msg_error_new_connexion>
		<msg_success_update_connexion trad="msg_success_update_connexion">Connection update completed successfully </msg_success_update_connexion>
		<msg_error_update_connexion trad="msg_error_update_connexion">An error occurred while updating the connection </msg_error_update_connexion>
		<creer_connexion trad="creer_connexion">Create connection in database</creer_connexion>
		<update_connexion trad="update_connexion">Update database</update_connexion>
		<creer_db trad="creer_db">Create database</creer_db>
		<msg_success_add_tarif_seances trad="msg_success_add_tarif_seances">Rates added successfully</msg_success_add_tarif_seances>
		<title_edit_seance_formule trad="title_edit_seance_formule">Edit a sales session for formula [formuleName]</title_edit_seance_formule>
		<title_edit_gestion_place trad="title_edit_gestion_place">Edit a sales rule for [manifName]</title_edit_gestion_place>
		<title_edit_offre_profil_acheteur trad="title_edit_offre_profil_acheteur">Update offers for buyer profile [profilAcheteurName]</title_edit_offre_profil_acheteur>
		<title_edit_offre trad="title_edit_offre">Update offers for buyer profile [offerName]</title_edit_offre>
		<msg_success_update_offre_profil_acheteur trad="msg_success_update_offre_profil_acheteur">Update of offers for this buyer profile completed successfully</msg_success_update_offre_profil_acheteur>
		<msg_error_update_offre_profil_acheteur trad="msg_error_update_offre_profil_acheteur">An error occurred while updating offers for this buyer profile</msg_error_update_offre_profil_acheteur>
		<aucune_offre_trouvee trad="aucune_offre_trouvee">No offer found</aucune_offre_trouvee>
		<add_new_gestion_place trad="add_new_gestion_place">Create a sales rule for [manifName] on [sessionDate]</add_new_gestion_place>
		<title_tarif trad="title_tarif">Rate selection</title_tarif>
		<title_reserves trad="title_reserves">Reserve selection</title_reserves>
		<title_mode_obtention trad="title_mode_obtention">Obtaining method</title_mode_obtention>
		<title_date trad="title_date">Date</title_date>
		<title_resume trad="title_resume">Summary</title_resume>
		<reserve_id trad="reserve_id">ID</reserve_id>
		<reserve_nom trad="reserve_nom">Name</reserve_nom>
		<title_copy_gestion_place trad="title_copy_gestion_place">Copy a sales rule for [manifName] on [sessionDate]</title_copy_gestion_place>
		<title_manifestations trad="title_manifestations">Event selection</title_manifestations>
		<msg_error_structure_id_empty trad="msg_error_structure_id_empty">Structure number is required</msg_error_structure_id_empty>
		<msg_error_structure_id_int trad="msg_error_structure_id_int">Structure ID must be an integer</msg_error_structure_id_int>
		<msg_error_structure_id_nb trad="msg_error_structure_id_nb">Structure ID must contain at least 4 digits</msg_error_structure_id_nb>
		<msg_error_structure_name_empty trad="msg_error_structure_name_empty">Structure name is required</msg_error_structure_name_empty>
		<msg_success_insert_structure trad="msg_success_insert_structure">Structure inserted successfully</msg_success_insert_structure>
		<msg_error_insert_structure trad="msg_error_insert_structure">A problem occurred during insertion</msg_error_insert_structure>
		<title_new_structure trad="title_new_structure">Create a new structure</title_new_structure>
		<structure_numero trad="structure_numero">Structure number</structure_numero>
		<structure_nom trad="structure_nom">Structure name</structure_nom>
		<manifestation_url_access trad="manifestation_url_access">URL for direct access</manifestation_url_access>
		<manifestation_url_test trad="manifestation_url_test">Test</manifestation_url_test>
		<msg_error_load_manifestations trad="msg_error_load_manifestations">No events</msg_error_load_manifestations>
		<msg_success_copy_url_link trad="msg_success_copy_url_link">Link url_link_text copied successfully</msg_success_copy_url_link>
		<msg_error_load_profil_acheteur trad="msg_error_load_profil_acheteur">Error loading structures</msg_error_load_profil_acheteur>
		<product_id trad="product_id">product id</product_id>
		<product_nom trad="product_nom">product name</product_nom>
		<product_stock trad="product_stock">product stock</product_stock>
		<msg_success_insert_product trad="msg_success_insert_product">Internet product added successfully</msg_success_insert_product>
		<msg_error_insert_product trad="msg_error_insert_product">A problem occurred while adding the internet product</msg_error_insert_product>
		<msg_success_update_product trad="msg_success_update_product">Product update completed successfully</msg_success_update_product>
		<msg_error_update_product trad="msg_error_update_product"> A problem occurred during the update</msg_error_update_product>
		<msg_success_delete_product trad="msg_success_delete_product">Product deleted successfully</msg_success_delete_product>
		<msg_error_delete_product trad="msg_error_delete_product">A problem occurred during product deletion</msg_error_delete_product>
		<status_deja_regle_vente trad="status_deja_regle_vente">Already a rule on this product!!</status_deja_regle_vente>
		<status_aucune_regle_vente trad="status_aucune_regle_vente">No rule on this product!!</status_aucune_regle_vente>
		<tooltip_add_product trad="tooltip_add_product">Add product</tooltip_add_product>
		<tooltip_update_product trad="tooltip_update_product">Update product</tooltip_update_product>
		<tooltip_delete_product trad="tooltip_delete_product">Delete product</tooltip_delete_product>
		<confirm_delete_product trad="confirm_delete_product">Are you sure you want to delete this product?</confirm_delete_product>
		<liste_offre trad="liste_offre">List of offers</liste_offre>
		<mettre_a_jour_droits trad="mettre_a_jour_droits">Update rights</mettre_a_jour_droits>
		<title_droits trad="title_droits">Rights for groups</title_droits>
		<manifestation_groupe_autorises trad="manifestation_groupe_autorises">Authorized event group(s)</manifestation_groupe_autorises>
		<title_tab_css_abo trad="title_tab_css_abo">Subscription CSS</title_tab_css_abo>
		<title_tab_css_park trad="title_tab_css_park">Park CSS</title_tab_css_park>
		<title_tab_css_indiv trad="title_tab_css_indiv">Individual site CSS</title_tab_css_indiv>
		<title_tab_css_reabo trad="title_tab_css_reabo">Re-subscription site CSS</title_tab_css_reabo>
		<title_tab_css_multi_sites trad="title_tab_css_multi_sites">Multi-sites CSS</title_tab_css_multi_sites>
		<title_tab_css_paiement trad="title_tab_css_paiement">Payment site CSS</title_tab_css_paiement>
		<creer_connexion_webtracing trad="creer_connexion_webtracing">Create a WebTracing connection</creer_connexion_webtracing>
		<repertoire_db_name trad="repertoire_db_name">DATA directory (.MDF and .LDF)</repertoire_db_name>
		<connexion_id trad="connexion_id">ID</connexion_id>
		<connexion_est_relier trad="connexion_est_relier">is linked</connexion_est_relier>
		<connexion_test trad="connexion_test">Test connection</connexion_test>
		<connexion_non_relier trad="connexion_non_relier">Connection not linked</connexion_non_relier>
		<connexion_relier trad="connexion_relier">Connection linked</connexion_relier>
		<relier_la_connexion trad="relier_la_connexion">Link the connection</relier_la_connexion>
		<ne_pas_relier_la_connexion trad="ne_pas_relier_la_connexion">Do not link the connection</ne_pas_relier_la_connexion>
		<msg_error_connexion_test trad="msg_error_connexion_test">Connection is null</msg_error_connexion_test>
		<msg_success_connexion_delier trad="msg_success_connexion_delier">Connection unlinked successfully</msg_success_connexion_delier>
		<msg_error_connexion_delier trad="msg_error_connexion_delier">An error occurred: connection was not unlinked</msg_error_connexion_delier>
		<msg_success_connexion_relier trad="msg_success_connexion_relier">Connection linked successfully</msg_success_connexion_relier>
		<msg_error_connexion_relier trad="msg_error_connexion_relier">An error occurred: connection was not linked</msg_error_connexion_relier>
		<msg_success_insert_database trad="msg_success_insert_database">Database created successfully </msg_success_insert_database>
		<msg_error_insert_database trad="msg_error_insert_database">An error occurred while saving the database</msg_error_insert_database>
		<msg_success_insert_connexion trad="msg_success_insert_connexion">Webtracing connection created successfully </msg_success_insert_connexion>
		<msg_error_insert_connexion trad="msg_error_insert_connexion">An error occurred while saving the Webtracing connection</msg_error_insert_connexion>
		<msg_error_contrainte_nom_empty trad="msg_error_contrainte_nom_empty">Constraint name is required</msg_error_contrainte_nom_empty>
		<msg_success_delete_contrainte trad="msg_success_delete_contrainte">Constraint deleted successfully</msg_success_delete_contrainte>
		<msg_error_delete_contrainte trad="msg_error_delete_contrainte">An error occurred while deleting the constraint</msg_error_delete_contrainte>
		<msg_success_insert_contrainte trad="msg_success_insert_contrainte">Constraint saved successfully</msg_success_insert_contrainte>
		<msg_error_insert_contrainte trad="msg_error_insert_contrainte">An error occurred while saving the constraint</msg_error_insert_contrainte>
		<confirm_delete_contrainte trad="confirm_delete_contrainte">Are you sure you want to delete the constraint?</confirm_delete_contrainte>
		<msg_success_update_contrainte trad="msg_success_update_contrainte">Constraint update saved successfully</msg_success_update_contrainte>
		<msg_error_update_contrainte trad="msg_error_update_contrainte">An error occurred while updating the constraint</msg_error_update_contrainte>
		<contrainte_id trad="contrainte_id">ID</contrainte_id>
		<produits_nom trad="produits_nom">Product(s) name</produits_nom>
		<contrainte_nom trad="contrainte_nom">Constraint name</contrainte_nom>
		<add_new_contrainte trad="add_new_contrainte">Create a new constraint</add_new_contrainte>
		<sql trad="sql">SQL is required</sql>
		<msg_error_offre_nom_empty trad="msg_error_offre_nom_empty">Offer name is required</msg_error_offre_nom_empty>
		<confirm_delete_offre trad="confirm_delete_offre">Are you sure you want to delete this offer?</confirm_delete_offre>
		<msg_success_delete_offre trad="msg_success_delete_offre">Offer deleted successfully</msg_success_delete_offre>
		<msg_error_delete_offre trad="msg_error_delete_offre">An error occurred while deleting the offer</msg_error_delete_offre>
		<msg_success_insert_offre trad="msg_success_insert_offre">Offer saved successfully</msg_success_insert_offre>
		<msg_error_insert_offre trad="msg_error_insert_offre">An error occurred while saving the offer</msg_error_insert_offre>
		<add_new_offre trad="add_new_offre">Add an offer</add_new_offre>
		<offre_nom trad="offre_nom">Offer name</offre_nom>
		<offre_id trad="offre_id">ID</offre_id>
		<add_new_profil_acheteur trad="add_new_profil_acheteur">Add a new buyer profile</add_new_profil_acheteur>
		<msg_error_libelle_empty trad="msg_error_libelle_empty">Label is required</msg_error_libelle_empty>
		<msg_error_paiement_empty trad="msg_error_paiement_empty">Customer account is required (0 if no customer account)</msg_error_paiement_empty>
		<msg_success_delete_profil_acheteur trad="msg_success_delete_profil_acheteur">Buyer profile deleted successfully</msg_success_delete_profil_acheteur>
		<msg_error_delete_profil_acheteur trad="msg_error_delete_profil_acheteur">An error occurred while deleting the buyer profile</msg_error_delete_profil_acheteur>
		<msg_success_insert_profil_acheteur trad="msg_success_insert_profil_acheteur">Buyer profile saved successfully</msg_success_insert_profil_acheteur>
		<msg_error_insert_profil_acheteur trad="msg_error_insert_profil_acheteur">An error occurred while saving the buyer profile</msg_error_insert_profil_acheteur>
		<msg_success_update_profil_acheteur trad="msg_success_update_profil_acheteur">Buyer profile updated successfully</msg_success_update_profil_acheteur>
		<msg_error_update_profil_acheteur trad="msg_error_update_profil_acheteur">An error occurred while updating the buyer profile</msg_error_update_profil_acheteur>
		<profil_acheteur_id trad="profil_acheteur_id">ID</profil_acheteur_id>
		<profil_acheteur_prenom trad="profil_acheteur_prenom">First name</profil_acheteur_prenom>
		<profil_acheteur_libelle trad="profil_acheteur_libelle">label</profil_acheteur_libelle>
		<profil_acheteur_compte_client trad="profil_acheteur_compte_client">Customer account (for invoice)</profil_acheteur_compte_client>
		<profil_acheteur_operateur trad="profil_acheteur_operateur">Payment operator</profil_acheteur_operateur>
		<profil_acheteur_consommateur trad="profil_acheteur_consommateur">Mandatory consumer</profil_acheteur_consommateur>
		<profil_acheteur_revendeur_mode trad="profil_acheteur_revendeur_mode">Reseller mode</profil_acheteur_revendeur_mode>
		<profil_acheteur_paiement_mode trad="profil_acheteur_paiement_mode">Payment mode</profil_acheteur_paiement_mode>
		<generer_pdf_test trad="generer_pdf_test">Generate a test PDF</generer_pdf_test>
		<select_maquette trad="select_maquette">Select a template</select_maquette>
		<msg_success_insert_logo trad="msg_success_insert_logo">Logo upload saved successfully</msg_success_insert_logo>
		<msg_error_upload_logo trad="msg_error_upload_logo">An error occurred during image upload</msg_error_upload_logo>
		<msg_error_chargement_maquette trad="msg_error_chargement_maquette">Templates could not be loaded</msg_error_chargement_maquette>
		<msg_error_aucune_maquette_configuree trad="aucune_maquette_configuree">No template configured </msg_error_aucune_maquette_configuree>
		<maquette_id trad="maquette_id">ID</maquette_id>
		<maquette_nom trad="maquette_nom">Template names</maquette_nom>
		<maquette_libelle trad="maquette_libelle">Label</maquette_libelle>
		<maquette_taille_x trad="maquette_taille_x">Position X</maquette_taille_x>
		<maquette_taille_y trad="maquette_taille_y">Position Y</maquette_taille_y>
		<maquette_path trad="maquette_path">Full path</maquette_path>
		<maquette_width trad="maquette_width">Width</maquette_width>
		<maquette_height trad="maquette_height">Height</maquette_height>
		<maquette_logo_existe trad="maquette_logo_existe">Exists</maquette_logo_existe>
		<maquette_logo_size trad="maquette_logo_size">Size: </maquette_logo_size>
		<maquette_choose_logo trad="maquette_choose_logo">Choose a new image</maquette_choose_logo>
		<apercu trad="apercu">Preview</apercu>
		<msg_error_size trad="msg_error_size">Current image cannot exceed: 20 KB</msg_error_size>
		<msg_error_upload_size trad="msg_error_upload_size">Current image is [tailleKO] KB. It cannot exceed: 20 KB</msg_error_upload_size>
		<inscription trad="inscription">Registration</inscription>
		<confirmation_commande trad="confirmation_commande">Order confirmation</confirmation_commande>
		<msg_success_insert_mail trad="msg_success_insert_mail">Text saved successfully</msg_success_insert_mail>
		<translate_title trad="translate_title">Translation</translate_title>
		<msg_success_save_translate_file trad="msg_success_save_translate_file">Translation file saved successfully</msg_success_save_translate_file>
		<msg_copy_url trad="msg_copy_url">Copy URL to clipboard</msg_copy_url>
		<login_auto trad="login_auto">Auto Login</login_auto>
		<afficher_tous_champs trad="afficher_tous_champs"> Show all fields</afficher_tous_champs>
		<afficher_champs_vide trad="afficher_champs_vide"> Show empty fields</afficher_champs_vide>
		<choose_language trad="choose_language">Choose a language </choose_language>
		<msg_error_identification_max_fini trad="msg_error_identification_max_fini">Your account is locked for 5 min</msg_error_identification_max_fini>
		<title_new_password trad="title_new_password">New password</title_new_password>
		<msg_identification_bad_password trad="msg_identification_bad_password">Identification error. You have [nbMaxAuthentification] attempts remaining</msg_identification_bad_password>
		<msg_error_compte_bloque trad="msg_error_compte_bloque">Your account is locked.</msg_error_compte_bloque>
		<msg_success_update_password trad="msg_success_update_password">New password saved successfully.</msg_success_update_password>
		<!--Menu-->
		<super_admin trad="super_admin">super_admin</super_admin>
		<new_structure trad="new_structure">New structure</new_structure>
		<gestion_utilisateurs trad="gestion_utilisateurs">user management</gestion_utilisateurs>
		<gestion_droits_utilisateurs trad="gestion_droits_utilisateurs">user rights management</gestion_droits_utilisateurs>
		<modifier_connexion trad="modifier_connexion">modify connection</modifier_connexion>
		<relier_connexion trad="relier_connexion">link connection</relier_connexion>
		<update_bdd trad="update_bdd">Database update </update_bdd>
		<traduire_application trad="traduire_application">translate application</traduire_application>
		<menu_gestion trad="menu_gestion">Management</menu_gestion>
		<menu_utilisateurs trad="menu_utilisateurs">Users</menu_utilisateurs>
		<menu_logos_maquettes_billets trad="menu_logos_maquettes_billets">Ticket logos and templates</menu_logos_maquettes_billets>
		<menu_vente_individuelle trad="menu_vente_individuelle">Individual sale</menu_vente_individuelle>
		<menu_vignettes_manifestations trad="menu_vignettes_manifestations">Event thumbnails</menu_vignettes_manifestations>
		<menu_css trad="menu_css">Stylesheet (CSS)</menu_css>
		<menu_commentaires_pages trad="menu_commentaires_pages"> Page comments</menu_commentaires_pages>
		<menu_tags trad="menu_tags">Analytics / Tracking Tags</menu_tags>
		<menu_options_manifs trad="menu_options_manifs">Event Options</menu_options_manifs>
		<menu_regles_ventes trad="menu_regles_ventes">Sales Rules</menu_regles_ventes>
		<menu_regle_vente_generales trad="menu_regle_vente_generales">General</menu_regle_vente_generales>
		<menu_regle_vente_particulieres trad="menu_regle_vente_particulieres">Specific</menu_regle_vente_particulieres>
		<menu_offres_particulieres trad="menu_offres_particulieres">Specific Offers</menu_offres_particulieres>
		<menu_contraintes_vente trad="menu_contraintes_vente">Sales Constraints</menu_contraintes_vente>
		<menu_profil_acheteurs trad="menu_profil_acheteurs"> Buyer Profiles</menu_profil_acheteurs>
		<menu_regle_vente_offres trad="menu_regle_vente_offres">Offer Sales Rules</menu_regle_vente_offres>
		<menu_produits trad="menu_produits">Products</menu_produits>
		<menu_url_manifs trad="menu_url_manifs">Event URLs</menu_url_manifs>
		<menu_foire_salon_parc trad="menu_foire_salon_parc">Fair/Exhibition/Park</menu_foire_salon_parc>
		<menu_abonnement trad="menu_abonnement">Subscription</menu_abonnement>
		<menu_gestion_mails trad="menu_gestion_mails">Mail text management </menu_gestion_mails>
		<menu_paiement trad="menu_paiement">Payment</menu_paiement>
		<menu_statistiques trad="menu_statistiques">Statistics</menu_statistiques>
		<menu_logs_paniers trad="menu_logs_paniers">Cart logs</menu_logs_paniers>
		<menu_statistiques_ventes trad="menu_statistiques_ventes">Sales statistics</menu_statistiques_ventes>
		<menu_situations_entrees trad="menu_situations_entrees">Entry situation</menu_situations_entrees>
		<menu_verif_test trad="menu_verif_test">Check fr sites</menu_verif_test>
		<menu_block_manifs_seances trad="menu_block_manifs_seances">Block Events/sessions</menu_block_manifs_seances>
		<menu_offre_catalog trad="menu_offre_catalog">Offer catalog configuration</menu_offre_catalog>
		<menu_block_settings_manifs_seances trad="menu_block_settings_manifs_seances">Block Events (Widget)</menu_block_settings_manifs_seances>
		<menu_waiting_manifs_seances trad="menu_waiting_manifs_seances">Events waiting list (Widget)</menu_waiting_manifs_seances>
		<menu_cross_selling_settings trad="menu_cross_selling_settings">Cross Selling Settings</menu_cross_selling_settings>
		<!--Fin Menu-->
		<!-- Politique mot de passe -->
		<text_danger trad="text_danger">text_danger</text_danger>
		<text_warning trad="text_warning">text_warning</text_warning>
		<text_success trad="text_success">text_success</text_success>
		<!-- fin Politique mot de passe -->
		<supprimer_gestion_place_selectionnees_formule trad="supprimer_gestion_place_selectionnees_formule">supprimer_gestion_place_selectionnees_formule </supprimer_gestion_place_selectionnees_formule>
		<msg_success_delete_all_gestion_place_in_formule trad="msg_success_delete_all_gestion_place_in_formule">Sales rules for formula [formuleName] deleted successfully</msg_success_delete_all_gestion_place_in_formule>
		<msg_error_delete_all_gestion_place_in_formule trad="msg_error_delete_all_gestion_place_in_formule">msg_error_delete_all_gestion_place_in_formule</msg_error_delete_all_gestion_place_in_formule>
		<tooltip_delete_formule trad="tooltip_delete_formule">delete formula</tooltip_delete_formule>
		<tooltip_update_formule trad="tooltip_update_formule">update formula</tooltip_update_formule>
		<tooltip_update_seance_in_formule trad="tooltip_update_seance_in_formule">update session in formula [formuleName]</tooltip_update_seance_in_formule>
		<tooltip_add_tarif_formule trad="tooltip_add_tarif_formule">add rates to formula</tooltip_add_tarif_formule>
		<tooltip_add_seances_formule trad="tooltip_add_seances_formule">add sessions to formula</tooltip_add_seances_formule>
		<tooltip_add_gestion_place_seance trad="tooltip_add_gestion_place_seance">add sales rule to session</tooltip_add_gestion_place_seance>
		<tooltip_update_gestion_place_seance trad="tooltip_update_gestion_place_seance">update sales rule for session</tooltip_update_gestion_place_seance>
		<tooltip_delete_gestion_place_seance trad="tooltip_delete_gestion_place_seance">delete rule from session</tooltip_delete_gestion_place_seance>
		<tooltip_copy_gestion_place_seance trad="tooltip_copy_gestion_place_seance">copy rule from session</tooltip_copy_gestion_place_seance>
		<web_user_id trad="web_user_id">web user id</web_user_id>
		<reset_password trad="reset_password">Forgot password</reset_password>
		<msg_success_mail_reset_password trad="msg_success_mail_reset_password">An email has just been sent</msg_success_mail_reset_password>
		<msg_error_mail_reset_password trad="msg_error_mail_reset_password">An error occurred while sending the email.</msg_error_mail_reset_password>
		<!--<msg_error_mail_empty trad="msg_error_mail_empty">Input error.</msg_error_mail_empty>-->
		<msg_error_hash_deja_utilise trad="msg_error_hash_deja_utilise">Link already used. Process is valid only once. Please click the link again</msg_error_hash_deja_utilise>
		<msg_error_compte_supprime trad="msg_error_compte_supprime">Account deleted. Please contact support.</msg_error_compte_supprime>
		<verif_sites trad="verif_sites">Restart test </verif_sites>
		<msg_error_verif_sites trad="msg_error_verif_sites">Warning !!! the word TEST is found in the following files: </msg_error_verif_sites>
		<msg_success_verif_sites trad="msg_success_verif_sites">No TEST word in files for this structure</msg_success_verif_sites>
		<msg_error_tarif_categ_configurer comm="lorsque le tableau retourne vide" trad="msg_error_tarif_categ_configurer">No category or rate configured</msg_error_tarif_categ_configurer>
		<msg_change_tabs comm="alerte pour savoir si l'on veut changer de tabs" trad="msg_change_tabs">work has been started. Do you want to save your work? Work will be lost</msg_change_tabs>
		<reset_cache_WT trad="reset_cache_WT">Clear cache</reset_cache_WT>
		<msg_success_delete_cache trad="msg_success_delete_cache">Cache deleted successfully</msg_success_delete_cache>
		<msg_error_delete_cache trad="msg_error_delete_cache">An error occurred!! </msg_error_delete_cache>
		<select_one_manifestation trad="select_one_manifestation">Select an event</select_one_manifestation>
		<select_one_seance trad="select_one_seance">Select a session</select_one_seance>
		<select_toutes_les_maquettes trad="select_toutes_les_maquettes">All templates</select_toutes_les_maquettes>
		<type_produit_application trad="type_produit_application">Application</type_produit_application>
		<type_produit_manifestation trad="type_produit_manifestation">Event</type_produit_manifestation>
		<type_produit_seance trad="type_produit_seance">Session</type_produit_seance>
		<statment_fund trad="statment_fund">Cash statement</statment_fund>
		<confirm_msg_abo_sur_plan trad="confirm_msg_abo_sur_plan">This option is only usable for closed subscriptions (sports type). Are you sure you want to enable this option?</confirm_msg_abo_sur_plan>
		<msg_error_none_structure_in_profil trad="msg_error_none_structure_in_profil">No structure linked to your profile</msg_error_none_structure_in_profil>
		<lbl_search trad="lbl_search">Search</lbl_search>
		<msg_error_play_job trad="msg_error_play_job">An error occurred during publication</msg_error_play_job>
		<msg_success_play_job trad="msg_success_play_job">Publication completed successfully</msg_success_play_job>
		<show_id trad="show_id">Show IDs</show_id>
		<has_tarif trad="has_tarif">Rate Ok</has_tarif>
		<status_tous_tarifs_ok trad="status_tous_tarifs_ok">all rates are ok</status_tous_tarifs_ok>
		<status_quelques_tarifs_ok trad="status_quelques_tarifs_ok">some rates are not configured</status_quelques_tarifs_ok>
		<msg_error_min_negatif trad="msg_error_min_negatif">Minimum number of seats cannot be negative</msg_error_min_negatif>
		<msg_error_max_negatif trad="msg_error_max_negatif">Maximum number of seats cannot be negative</msg_error_max_negatif>
		<msg_error_start_date_negatif trad="msg_error_start_date_negatif">Start date cannot be negative</msg_error_start_date_negatif>
		<msg_error_end_date_negatif trad="msg_error_end_date_negatif">End date cannot be negative</msg_error_end_date_negatif>
		<msg_success_update_offre_produit trad="msg_success_update_offre_produit">msg_success_update_offre_produit</msg_success_update_offre_produit>
		<msg_error_update_offre_produit trad="msg_error_update_offre_produit">msg_error_update_offre_produit</msg_error_update_offre_produit>
		<title_edit_contrainte_offre trad="title_edit_contrainte_offre">Edit constraint [contrainteName]</title_edit_contrainte_offre>
		<!-- CASH STATEMENT -->
		<lbl_sum trad="lbl_sum">Total: </lbl_sum>
		<lbl_sum_entries_for_event trad="lbl_sum_entries_for_event">Sum of entries for event:</lbl_sum_entries_for_event>
		<lbl_nbr_total_entries trad="lbl_nbr_total_entries">total nbr entries</lbl_nbr_total_entries>
		<lbl_sum_total trad="lbl_sum_total">Total sum</lbl_sum_total>
		<lbl_seance_du trad="lbl_seance_du">Session of: </lbl_seance_du>
		<col_dossier_etat trad="col_dossier_etat">Folder status</col_dossier_etat>
		<col_type_tarif_nom trad="col_type_tarif_nom">rate type name </col_type_tarif_nom>
		<col_montant1 trad="col_montant1">amount </col_montant1>
		<col_categ_nom trad="col_categ_nom">category name </col_categ_nom>
		<col_nbrEntrees trad="col_nbrEntrees">nbr entries </col_nbrEntrees>
		<col_sumSeance trad="col_sumSeance">sum on sessions </col_sumSeance>
		<col_manif_id trad="col_manif_id">event id</col_manif_id>
		<col_manifestation_nom trad="col_manifestation_nom">event</col_manifestation_nom>
		<col_seance_date_deb trad="col_seance_date_deb">start date</col_seance_date_deb>
		<col_type_tarif_id trad="col_type_tarif_id">rate id</col_type_tarif_id>
		<col_seance_id trad="col_seance_id">session id</col_seance_id>
		<col_categorie_id trad="col_categorie_id">category id</col_categorie_id>
		<!-- FIN CASH STATEMENT -->
		<!--  statistiques_paniers. -->
		<lbl_title_panier trad="lbl_title_panier">CART</lbl_title_panier>
		<lbl_col_panier_id trad="lbl_col_panier_id">Cart ID</lbl_col_panier_id>
		<lbl_col_etat trad="lbl_col_etat">lbl_col_etat</lbl_col_etat>
		<lbl_col_commande trad="lbl_col_commande">order</lbl_col_commande>
		<lbl_col_transaction trad="lbl_col_transaction">Transaction</lbl_col_transaction>
		<lbl_col_certificate trad="lbl_col_certificate">Certificate</lbl_col_certificate>
		<lbl_col_card_number trad="lbl_col_card_number">Card number</lbl_col_card_number>
		<lbl_title_entree trad="lbl_title_entree">ENTRIES</lbl_title_entree>
		<lbl_col_basket_number trad="lbl_col_basket_number">lbl_col_basket_number</lbl_col_basket_number>
		<lbl_col_entree_id trad="lbl_col_entree_id">lbl_col_entree_id</lbl_col_entree_id>
		<lbl_col_vts_id trad="lbl_col_vts_id">lbl_col_vts_id</lbl_col_vts_id>
		<lbl_col_rang trad="lbl_col_rang">lbl_col_rang</lbl_col_rang>
		<lbl_col_siege trad="lbl_col_siege">lbl_col_siege</lbl_col_siege>
		<lbl_col_description_seance trad="lbl_col_description_seance">lbl_col_description_seance</lbl_col_description_seance>
		<col_frais trad="col_frais">col_frais</col_frais>
		<col_type_envoi_id trad="col_type_envoi_id">col_type_envoi_id</col_type_envoi_id>
		<col_type_envoi trad="col_type_envoi">delivery method</col_type_envoi>
		<col_consumer_id trad="col_consumer_id">col_consumer_id</col_consumer_id>
		<col_valeur trad="col_valeur">col_valeur</col_valeur>
		<col_section trad="col_section">col_section</col_section>
		<col_zone trad="col_zone">col_zone</col_zone>
		<col_etage trad="col_etage">col_etage</col_etage>
		<lbl_title_produit trad="lbl_title_produit">PRODUCT</lbl_title_produit>
		<lbl_title_frais trad="lbl_title_frais">lbl_title_frais</lbl_title_frais>
		<col_panier_produit_id trad="col_panier_produit_id">col_panier_produit_id</col_panier_produit_id>
		<col_produit_id trad="col_produit_id">col_produit_id</col_produit_id>
		<col_nombre trad="col_nombre">col_nombre</col_nombre>
		<col_montant trad="col_montant">col_montant</col_montant>
		<col_type_ligne trad="col_type_ligne">line_type</col_type_ligne>
		<lbl_title_entree_abo trad="lbl_title_entree_abo">SUBSCRIPTION ENTRIES</lbl_title_entree_abo>
		<col_panier_numero trad="col_panier_numero">col_panier_numero</col_panier_numero>
		<lbl_title_produit_abo trad="lbl_title_produit_abo">SUBSCRIPTION PRODUCT</lbl_title_produit_abo>
		<lbl_title_frais_abo trad="lbl_title_frais_abo">FEES</lbl_title_frais_abo>
		<col_frais_abo trad="col_frais_abo">col_frais_abo</col_frais_abo>
		<!-- FIN statistiques_paniers. -->
		<msg_success_update_block_manifs_seance trad="msg_success_update_block_manifs_seance">Update completed successfully</msg_success_update_block_manifs_seance>
		<msg_error_update_block_manifs_seance trad="msg_error_update_block_manifs_seance">A problem occurred during update</msg_error_update_block_manifs_seance>
		<msg_queuing_update_success trad="msg_queuing_update_success">Queue configurations updated successfully.</msg_queuing_update_success>
		<msg_queuing_create_tables_success trad="msg_queuing_create_tables_success">Table creation completed successfully.</msg_queuing_create_tables_success>
		<dossier_etat trad="dossier_etat">T</dossier_etat>
		<btn_close trad="btn_close">Close</btn_close>
		<lbl_virement trad="lbl_virement">TRANSFER</lbl_virement>
		<!-- cash reports profil acheteur (-->
		<lbl_amount_revendeur trad="lbl_amount_revendeur">Reseller amount</lbl_amount_revendeur>
		<lbl_nbr_seats trad="lbl_nbr_seats">Nbr seats </lbl_nbr_seats>
		<lbl_payment_date trad="lbl_payment_date">Purchase date</lbl_payment_date>
		<msg_error_date_empty trad="msg_error_date_empty">Dates cannot be empty</msg_error_date_empty>
		<lbl_select_profil_acheteur trad="lbl_select_profil_acheteur">Select a profile</lbl_select_profil_acheteur>
		<btn_create_queue trad="btn_create_queue">Create a queue</btn_create_queue>
		<!-- Cash Statement Column Headers - English Translations -->
		<reference_unique_billet trad="reference_unique_billet">Unique ticket reference</reference_unique_billet>
		<dernier_etat_place trad="dernier_etat_place">Last seat status for the period</dernier_etat_place>
		<id_filiere_vente trad="id_filiere_vente">Sales channel ID</id_filiere_vente>
		<nom_filiere_vente trad="nom_filiere_vente">Sales channel name for tickets</nom_filiere_vente>
		<id_manifestation trad="id_manifestation">Event ID</id_manifestation>
		<nom_manifestation trad="nom_manifestation">Event name</nom_manifestation>
		<groupe_manifestations trad="groupe_manifestations">Event group</groupe_manifestations>
		<!-- Additional column headers found in debug logs -->
		<manif_id trad="manif_id">Event ID</manif_id>
		<manifestation_nom trad="manifestation_nom">Event name</manifestation_nom>
		<seance_date_deb trad="seance_date_deb">Session start date</seance_date_deb>
		<type_tarif_id trad="type_tarif_id">Rate type ID</type_tarif_id>
		<seance_id trad="seance_id">Session ID</seance_id>
		<type_tarif_nom trad="type_tarif_nom">Rate type name</type_tarif_nom>
		<montant1 trad="montant1">Amount 1</montant1>
		<categorie_id trad="categorie_id">Category ID</categorie_id>
		<categ_nom trad="categ_nom">Category name</categ_nom>
		<nbrEntrees trad="nbrEntrees">Number of entries</nbrEntrees>
		<sumSeance trad="sumSeance">Session total</sumSeance>
		<dossier_etat trad="dossier_etat">Folder status</dossier_etat>
		<!-- Additional translations from detailed view -->
		<genre_manifestation trad="genre_manifestation">Event genre</genre_manifestation>
		<sous_genre_manifestation trad="sous_genre_manifestation">Event sub-genre</sous_genre_manifestation>
		<cible_manifestation trad="cible_manifestation">Event target audience</cible_manifestation>
		<date_debut_seance trad="date_debut_seance">Session start date</date_debut_seance>
		<id_categorie trad="id_categorie">Category ID</id_categorie>
		<nom_categorie trad="nom_categorie">Category name</nom_categorie>
		<id_tarif trad="id_tarif">Rate ID</id_tarif>
		<nom_tarif trad="nom_tarif">Rate name</nom_tarif>
		<!-- Montant columns with capital M -->
		<Montant_1 trad="Montant_1">Amount 1</Montant_1>
		<Montant_2 trad="Montant_2">Amount 2</Montant_2>
		<Montant_3 trad="Montant_3">Amount 3</Montant_3>
		<Montant_4 trad="Montant_4">Amount 4</Montant_4>
		<Montant_5 trad="Montant_5">Amount 5</Montant_5>
		<Montant_6 trad="Montant_6">Amount 6</Montant_6>
		<Montant_7 trad="Montant_7">Amount 7</Montant_7>
		<Montant_8 trad="Montant_8">Amount 8</Montant_8>
		<Montant_9 trad="Montant_9">Amount 9</Montant_9>
		<Montant_10 trad="Montant_10">Amount 10</Montant_10>
		<!-- Location and ticket details -->
		<ref_externe trad="ref_externe">External reference</ref_externe>
		<motif trad="motif">Reason</motif>
		<codebarre trad="codebarre">Barcode</codebarre>
		<rang trad="rang">Row</rang>
		<siege trad="siege">Seat</siege>
		<denom_nom trad="denom_nom">Denomination</denom_nom>
		<Zone trad="Zone">Zone</Zone>
		<Etage trad="Etage">Floor</Etage>
		<Section trad="Section">Section</Section>
		<lieu_nom trad="lieu_nom">Location name</lieu_nom>
		<!-- Contact and identity details -->
		<postal_tel5 trad="postal_tel5">Phone 5</postal_tel5>
		<postal_tel6 trad="postal_tel6">Phone 6</postal_tel6>
		<postal_tel7 trad="postal_tel7">Phone 7</postal_tel7>
		<menu_module_traductions trad="menu_module_traductions">Module translations</menu_module_traductions>
		<menu_user_queuing_settings trad="menu_user_queuing_settings">Configuration</menu_user_queuing_settings>
		<menu_user_infocomps_settings trad="menu_user_infocomps_settings">Infocomps Configuration</menu_user_infocomps_settings>
		<menu_interfaces trad="menu_interfaces">Interfaces</menu_interfaces>
		<menu_queuing trad="menu_queuing">Queue</menu_queuing>
		<menu_queuing_settings trad="menu_queuing_settings">Configuration</menu_queuing_settings>
		<menu_queuing_statistics trad="menu_queuing_statistics">Statistics</menu_queuing_statistics>
		<printer_proof trad="printer_proof">Print proof</printer_proof>
		<menu_cash_reports trad="menu_cash_reports">Cash reports</menu_cash_reports>
		<menu_distanciation_management trad="menu_distanciation_management">Distance reset</menu_distanciation_management>
		<menu_distanciation_mana trad="menu_distanciation_mana">Distance reset</menu_distanciation_mana>
		<menu_indiv_advanced_parameters trad="menu_indiv_advanced_parameters">Advanced parameters</menu_indiv_advanced_parameters>
		<menu_step_show_choix_sur_plan trad="menu_step_show_choix_sur_plan">Plan selection button management</menu_step_show_choix_sur_plan>
		<select_all_prices trad="select_all_prices">Select all prices</select_all_prices>
		<menu_pass_culture trad="menu_pass_culture">Culture pass</menu_pass_culture>
		<menu_pass_culture_settings trad="menu_pass_culture_settings">Settings</menu_pass_culture_settings>
		<menu_collective_offers trad="menu_collective_offers">Collective offers</menu_collective_offers>
		<menu_individual_offers trad="menu_individual_offers">Individual offers</menu_individual_offers>
		<menu_customer trad="menu_customer">Customer area</menu_customer>
		<menu_indiv_theme trad="menu_indiv_theme">Theme customization</menu_indiv_theme>
		<title_translate_customer trad="title_translate_customer">Translations for CUSTOMER platform</title_translate_customer>
		<menu_collective_offer trad="menu_collective_offer">Collective offers</menu_collective_offer>
		<menu_individual_offer trad="menu_individual_offer">Individual offers</menu_individual_offer>
		<menu_create_individual_offer trad="menu_create_individual_offer">Create individual offer</menu_create_individual_offer>
		<menu_update_individual_offer trad="menu_update_individual_offer">Update individual offer</menu_update_individual_offer>
		<menu_create_collective_offer trad="menu_create_collective_offer">Create collective offer</menu_create_collective_offer>
		<menu_update_collective_offer trad="menu_update_collective_offer">Update collective offer</menu_update_collective_offer>
		<menu_block_settings_manifs trad="menu_block_settings_manifs">Block Events/Sessions</menu_block_settings_manifs>
		<menu_waiting_manifs_settings trad="menu_waiting_manifs_settings">Waiting List Events (Widget)</menu_waiting_manifs_settings>
		<file_attente trad="file_attente">Queue</file_attente>
		<parametrage trad="parametrage">Configuration</parametrage>
		<maintenance trad="maintenance">Maintenance</maintenance>
		<file_attente_v1 trad="file_attente_v1">Queue V1</file_attente_v1>
		<file_attente_v2 trad="file_attente_v2">Queue V2</file_attente_v2>
		<inactif trad="inactif">Inactive</inactif>
		<actif trad="actif">Active</actif>
		<sauvegarder_modifications trad="sauvegarder_modifications">Save changes</sauvegarder_modifications>
		<msg_queuing_dont_warning_templates trad="msg_queuing_dont_warning_templates">Please verify the existence of your templates for Maintenance and Queuing V1 before saving your changes</msg_queuing_dont_warning_templates>
		<btn_queuing_save trad="btn_queuing_save">Save</btn_queuing_save>
		<lbl_queuing trad="lbl_queuing">Queue</lbl_queuing>
		<lbl_queuing_maintenance trad="lbl_queuing_maintenance">Maintenance</lbl_queuing_maintenance>
		<lbl_queuing_v1 trad="lbl_queuing_v1">Queue V1</lbl_queuing_v1>
		<lbl_queuing_v2 trad="lbl_queuing_v2">Queue V2</lbl_queuing_v2>
		<lbl_queuing_v1_maxutilisateurs trad="lbl_queuing_v1_maxutilisateurs">Max. number of people</lbl_queuing_v1_maxutilisateurs>
		<lbl_queuing_v1_delaysecondes trad="lbl_queuing_v1_delaysecondes">Delay before inactivity</lbl_queuing_v1_delaysecondes>
		<lbl_queuing_v1_fileattenteindiv trad="lbl_queuing_v1_fileattenteindiv">INDIV queue name</lbl_queuing_v1_fileattenteindiv>
		<lbl_queuing_v1_fileattenteabo trad="lbl_queuing_v1_fileattenteabo">ABO queue name</lbl_queuing_v1_fileattenteabo>
		<lbl_queuing_v2_dbtype trad="lbl_queuing_v2_dbtype">Database type</lbl_queuing_v2_dbtype>
		<lbl_queuing_v2_maxutilisateurs trad="lbl_queuing_v2_maxutilisateurs">Max. number of people</lbl_queuing_v2_maxutilisateurs>
		<lbl_queuing_v2_userspartic trad="lbl_queuing_v2_userspartic">Number of people per passage</lbl_queuing_v2_userspartic>
		<lbl_queuing_v2_isopen trad="lbl_queuing_v2_isopen">Queue opening</lbl_queuing_v2_isopen>
		<lbl_queuing_v2_isactivefieldtome trad="lbl_queuing_v2_isactivefieldtome">Use Google Captcha V3</lbl_queuing_v2_isactivefieldtome>
		<lbl_queuing_v2_isactivite trad="lbl_queuing_v2_isactivite">Connection URL to platform</lbl_queuing_v2_isactivite>
		<lbl_queuing_v2_urllogo trad="lbl_queuing_v2_urllogo">Generic queuing URL</lbl_queuing_v2_urllogo>
		<lbl_queuing_v2_url trad="lbl_queuing_v2_url">Queuing URL for Indiv</lbl_queuing_v2_url>
		<lbl_queuing_v2_url_indiv trad="lbl_queuing_v2_url_indiv">Queuing URL for Indiv</lbl_queuing_v2_url_indiv>
		<lbl_queuing_v2_url_abo trad="lbl_queuing_v2_url_abo">Queuing URL for Abo</lbl_queuing_v2_url_abo>
		<nom_maintenance trad="nom_maintenance">Maintenance name</nom_maintenance>
		<sec trad="sec">sec</sec>
		<lbl_queuing_maintenance_name trad="lbl_queuing_maintenance_name">Maintenance name</lbl_queuing_maintenance_name>
		<lbl_queuing_v1_seconds trad="lbl_queuing_v1_seconds">seconds</lbl_queuing_v1_seconds>
		<lbl_queuing_v1_fileattenteindiv trad="lbl_queuing_v1_fileattenteindiv">INDIV queue name</lbl_queuing_v1_fileattenteindiv>
		<lbl_queuing_v1_fileattenteabo trad="lbl_queuing_v1_fileattenteabo">ABO queue name</lbl_queuing_v1_fileattenteabo>
		<database_type trad="database_type">Database type</database_type>
		<queue_opening trad="queue_opening">Queue opening</queue_opening>
		<queuing_url_for_indiv trad="queuing_url_for_indiv">Queuing URL for Indiv</queuing_url_for_indiv>
		<title_queuing_maintenance trad="title_queuing_maintenance">Maintenance</title_queuing_maintenance>
		<title_queuing_v1 trad="title_queuing_v1">Queue V1</title_queuing_v1>
		<title_queuing_v2 trad="title_queuing_v2">Queue V2</title_queuing_v2>
		<lbl_queuing_v1_maxactiveusers trad="lbl_queuing_v1_maxactiveusers">Max. number of people</lbl_queuing_v1_maxactiveusers>
		<lbl_queuing_v1_delayinseconds trad="lbl_queuing_v1_delayinseconds">Delay before inactivity</lbl_queuing_v1_delayinseconds>
		<lbl_queuing_v1_filenameindiv trad="lbl_queuing_v1_filenameindiv">INDIV queue name</lbl_queuing_v1_filenameindiv>
		<lbl_queuing_v1_filenameabo trad="lbl_queuing_v1_filenameabo">ABO queue name</lbl_queuing_v1_filenameabo>
		<lbl_queuing_v2_maxactiveusers trad="lbl_queuing_v2_maxactiveusers">Max. number of people</lbl_queuing_v2_maxactiveusers>
		<lbl_queuing_v2_userspertick trad="lbl_queuing_v2_userspertick">Number of people per passage</lbl_queuing_v2_userspertick>
		<lbl_queuing_v2_IsActiveFileAttente trad="lbl_queuing_v2_IsActiveFileAttente">Queue activation</lbl_queuing_v2_IsActiveFileAttente>
		<lbl_queuing_v2_iscaptcha trad="lbl_queuing_v2_iscaptcha">Use Google Captcha V3</lbl_queuing_v2_iscaptcha>
		<lbl_queuing_v2_urltogo trad="lbl_queuing_v2_urltogo">Redirect URL to platform</lbl_queuing_v2_urltogo>
		<msg_queuing_final_url trad="msg_queuing_final_url">the final address will be:</msg_queuing_final_url>
		<btn_create_queue trad="btn_create_queue">Prepare a queue</btn_create_queue>
		<msg_queuing_alert_warning_templates trad="msg_queuing_alert_warning_templates">Please verify the existence of your templates for Maintenance and Queuing V1 before saving your changes.</msg_queuing_alert_warning_templates>
		<lbl_queuing_switch_on trad="lbl_queuing_switch_on">Active</lbl_queuing_switch_on>
		<lbl_queuing_switch_off trad="lbl_queuing_switch_off">Inactive</lbl_queuing_switch_off>
		<lbl_queuing_switch_open trad="lbl_queuing_switch_open">Open</lbl_queuing_switch_open>
		<lbl_queuing_switch_close trad="lbl_queuing_switch_close">Closed</lbl_queuing_switch_close>
		<login trad="login">Username</login>
		<email trad="email">Email</email>
		<profil trad="profil">Profile</profil>
		<manifestation_groupe_autorises trad="manifestation_groupe_autorises">Authorized Event Groups</manifestation_groupe_autorises>
		<structures trad="structures">Structures</structures>
		<actions trad="actions">Actions</actions>
		<all_results trad="all_results">All</all_results>
		<title_translate_indiv trad="title_translate_indiv">Individual Translations</title_translate_indiv>
	</Generals>
</Root>