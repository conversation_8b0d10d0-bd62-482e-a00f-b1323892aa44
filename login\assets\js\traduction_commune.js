﻿var _baseUrl = "/pages/Resources/translate.";
var _baseUrlDefault = "/pages/Resources/Default/translate.";
var arrTermsTranslateDefaultFr = new Array();
var arrTermsTranslateLoaded = new Array();

var sAjaxSourceUrl = "/basepage.aspx/";

var _url = "";
function fileExists(url) {
    if (url) {
        var req = new XMLHttpRequest();
        req.open('GET', url, false);
        req.send();
        return req.status == 200;
    } else {
        return false;
    }
}



function LoadTranslateInArray(isDefault, getXml, lang) {

    var language;
    if (lang === undefined)
        lang = "fr";

    console.log("=== LoadTranslateInArray ===");
    console.log("Paramètres: isDefault=" + isDefault + ", getXml=" + getXml + ", lang=" + lang);

    if (IsIE() || IsSafari() || IsFirefox())
        language = navigator.language.split('-')[0];
    else
        language = window.navigator.language;

    if (getXml === undefined) {
        getXml = false;
    }

    // NE PAS écraser le paramètre lang si il est explicitement fourni
    if (!isDefault && !getXml && lang === "fr") {
        // Seulement utiliser la langue du navigateur si aucune langue spécifique n'est demandée
        lang = language;
        console.log("Utilisation de la langue du navigateur: " + lang);
    } else {
        console.log("Utilisation de la langue spécifiée: " + lang);
    }

    // Essayer plusieurs chemins possibles pour le fichier XML
    var possiblePaths = [
        "/pages/Resources/translate." + lang + ".xml",
        "pages/Resources/translate." + lang + ".xml",
        "../pages/Resources/translate." + lang + ".xml",
        "./pages/Resources/translate." + lang + ".xml"
    ];

    var xmlLoaded = false;

    for (var i = 0; i < possiblePaths.length && !xmlLoaded; i++) {
        var xmlUrl = possiblePaths[i];

        $.ajax({
            type: "GET",
            url: xmlUrl,
            async: false,
            dataType: "xml",
            beforeSend: function(xhr) {
                xhr.overrideMimeType("text/xml; charset=utf-8");
            },
            success: function (xml) {
                console.log("=== CHARGEMENT XML RÉUSSI ===");
                console.log("URL: " + xmlUrl);
                console.log("XML reçu:", xml);
                xmlLoaded = true;

                // Parser le XML et extraire les traductions
                $(xml).find('*[trad]').each(function() {
                    var obj = {};
                    var tradKey = $(this).attr('trad');

                    // Essayer plusieurs méthodes pour récupérer le texte
                    var tradValue = $(this).text();
                    if (!tradValue || tradValue.trim() === '') {
                        tradValue = this.textContent || this.innerText || '';
                    }

                    // Debug pour l'élément dossier_etat
                    if (tradKey === 'dossier_etat') {
                        console.log("=== DEBUG dossier_etat ===");
                        console.log("tradKey:", tradKey);
                        console.log("$(this).text():", $(this).text());
                        console.log("this.textContent:", this.textContent);
                        console.log("this.innerText:", this.innerText);
                        console.log("tradValue final:", tradValue);
                    }

                    obj[tradKey] = tradValue;

                    if (isDefault) {
                        arrTermsTranslateDefaultFr.push(obj);
                    } else {
                        arrTermsTranslateLoaded.push(obj);
                    }
                });

                console.log("Nombre de traductions chargées: " + (isDefault ? arrTermsTranslateDefaultFr.length : arrTermsTranslateLoaded.length));
            }
        });
    }

    // Si aucun fichier XML n'a pu être chargé, essayer l'appel AJAX original
    if (!xmlLoaded) {
        console.log("Aucun fichier XML trouvé, tentative avec l'appel AJAX original...");

        var sData = JSON.stringify({
            langCode: lang,
            isDefault: isDefault,
            getXml: getXml
        });

        $.ajax({
            type: "POST",
            url:  "/basepage.aspx/GetXml",
            async: false,
            contentType: "application/json; charset=utf-8",
            data: sData,
            success: function (xml) {
                if (xml.d != "") {
                    if (getXml) {
                        resultXml = xml.d;
                    }

                    var arrayXml = JSON.parse(xml.d);

                    $.each(arrayXml, function (i, item) {
                        var obj = {};
                        obj[item.Trad] = item.Value;

                        if (isDefault) {
                            arrTermsTranslateDefaultFr.push(obj);
                        } else {
                            arrTermsTranslateLoaded.push(obj);
                        }
                    });
                }
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                console.log("Erreur AJAX: " + textStatus);
            }
        });
    }

    if (getXml) {
        return JSON.parse(resultXml);
    }

}


//searchTerm = term a rechercher attribute trad
//getXml = true ou false pour savoir si on retourne juste le xml charger par defautl a false

function LoadTranslateInArrayOld(isDefault, getXml, lang) {

    if (lang === undefined) {
        lang = "fr";
    }


    if (getXml === undefined) {
        getXml = false;
    }

    var sData = JSON.stringify({
        langCode: lang,
        isDefault: isDefault,
        getXml: getXml
    });


    var resultXml;
    $.ajax({
        type: "POST",
        url: "/basepage.aspx/GetXml",
        async: false,
        contentType: "application/json; charset=utf-8", //ne fonctionne pas en local ?        
        data: sData,
        success: function (xml) {

            // if (retour.documentElement.textContent != "") { en local il faut utilisé retour.documentElement.textContent
            if (xml.d != "") {
                if (getXml) {
                    resultXml = xml.d;
                }

                var arrayXml = JSON.parse(xml.d);
                $.each(arrayXml, function (i, item) {
                    var obj = {};
                    obj[item.Trad] = item.Value;

                    if (isDefault) {
                        arrTermsTranslateDefaultFr.push(obj);
                    } else {
                        arrTermsTranslateLoaded.push(obj);
                    }
                });
            }
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            alert(textStatus);
        },
        complete: function () {
            //  waitingDialog.hide();
        }
    });


    if (getXml) {
        return JSON.parse(resultXml);
    }

}


var isLoaded = false;
var currentLanguage = "fr"; // Langue par défaut

// Fonction pour récupérer la langue sélectionnée
function getSelectedLanguage() {
    // Essayer de récupérer la langue depuis différentes sources
    if (typeof ddlLang !== 'undefined' && ddlLang !== "") {
        return ddlLang;
    }
    if ($("#selectLang").length > 0 && $("#selectLang").val() !== "") {
        return $("#selectLang").val();
    }
    if ($("#selectLangues").length > 0 && $("#selectLangues").val() !== "") {
        return $("#selectLangues").val();
    }

    // Si currentLanguage a été forcée, l'utiliser
    if (typeof currentLanguage !== 'undefined' && currentLanguage !== 'fr') {
        return currentLanguage;
    }

    // Par défaut, utiliser français (pas la langue du navigateur pour éviter les problèmes)
    return 'fr';
}

function ReadXmlTranslate(searchTerm) {
    console.log("=== ReadXmlTranslate appelée avec: " + searchTerm + " ===");

    var result = "";

    // Vérifier si la langue a changé ou si ce n'est pas encore chargé
    var selectedLang = getSelectedLanguage();
    if (!isLoaded || currentLanguage !== selectedLang) {
        console.log("Chargement des traductions pour la langue: " + selectedLang);
        currentLanguage = selectedLang;

        // Réinitialiser les tableaux
        arrTermsTranslateLoaded = [];
        arrTermsTranslateDefaultFr = [];

        LoadTranslateInArray(false, false, selectedLang);
        LoadTranslateInArray(true, false, selectedLang);
        isLoaded = true;
        console.log("loaded Translate in array for language: " + selectedLang);
    }

    // Recherche dans les traductions chargées (toujours faire la recherche)
    if (arrTermsTranslateLoaded.length > 0) {
        result = $.map(arrTermsTranslateLoaded, function (val) {
            return val[searchTerm];
        });
    }

    if (result.length == 0) {
        if (arrTermsTranslateDefaultFr.length > 0) {
            result = $.map(arrTermsTranslateDefaultFr, function (val) {
                return val[searchTerm];
            });
        }
    }

    if (result.length == 0) {
        console.warn("Traduction manquante pour: " + searchTerm); // Message plus discret
        return searchTerm; // Retourne la clé si pas de traduction trouvée
    }

    return result[0] || searchTerm; // Sécurité supplémentaire
}



// Fonction pour forcer le rechargement des traductions
function ReloadTranslations(newLanguage) {
    console.log("=== ReloadTranslations appelée pour la langue: " + newLanguage + " ===");

    // Forcer le rechargement
    isLoaded = false;
    currentLanguage = newLanguage || getSelectedLanguage();

    // Réinitialiser les tableaux
    arrTermsTranslateLoaded = [];
    arrTermsTranslateDefaultFr = [];

    // Recharger les traductions
    LoadTranslateInArray(false, false, currentLanguage);
    LoadTranslateInArray(true, false, currentLanguage);
    isLoaded = true;

    // Appliquer les nouvelles traductions
    LaunchTraduction();
}

/**
 * Force la langue pour cette session (quand aucun sélecteur n'est trouvé)
 */
function ForceLanguage(lang) {
    console.log("=== ForceLanguage appelée pour: " + lang + " ===");

    // Mettre à jour ou créer un sélecteur virtuel
    if ($('#selectLang').length === 0) {
        // Créer un sélecteur virtuel
        $('body').append('<select id="selectLang" style="display:none"><option value="' + lang + '" selected>' + lang + '</option></select>');
        console.log("Sélecteur virtuel créé pour: " + lang);
    } else {
        // Mettre à jour le sélecteur existant
        $('#selectLang').val(lang);
        console.log("Sélecteur virtuel mis à jour pour: " + lang);
    }

    // Forcer la mise à jour de currentLanguage
    currentLanguage = lang;

    // Vider complètement les caches
    arrTermsTranslateLoaded = [];
    arrTermsTranslateDefaultFr = [];
    isLoaded = false;

    console.log("Cache vidé, rechargement pour: " + lang);

    // Recharger les traductions avec la nouvelle langue
    LoadTranslateInArray(false, false, lang);
    LoadTranslateInArray(true, false, lang);
    isLoaded = true;

    // Appliquer les traductions
    LaunchTraduction();
}

function LaunchTraduction() {
    /* $.each($('*[data-trad]'), function (indx, item) {
    //ReadXmlTranslate($(item).data('trad'));
    $(item).html(ReadXmlTranslate($(item).data('trad')).text());
    });*/

    // Vérifier si la langue a changé
    var selectedLang = getSelectedLanguage();
    if (currentLanguage !== selectedLang) {
        ReloadTranslations(selectedLang);
        return; // La fonction ReloadTranslations appellera LaunchTraduction à nouveau
    }

    if (!isLoaded) {
        LoadTranslateInArray(false, false, selectedLang);
        LoadTranslateInArray(true, false, selectedLang);

        isLoaded = true;
    }

    $.each($('*[data-trad]'), function (indx, item) {
        //ReadXmlTranslate($(item).data('trad'));

        if ($(this).is("input"))
            $(item).val(ReadXmlTranslate($(item).data('trad')));
        else
            $(item).html(ReadXmlTranslate($(item).data('trad')));
    });
}

function LoadFileTranslate(isDefault, lang) {
    var language = "fr";
    if(lang != undefined)
        language=lang;



    if (isDefault == false && lang == undefined) {
        language = window.navigator.userLanguage || window.navigator.language;
        // language = "de";


        if (IsIE() || IsSafari() || IsFirefox())
            language = navigator.language.split('-')[0];
        else
            language = window.navigator.language;
    }
    var _url = "";

   
    var _urlLang = _baseUrl + language + ".xml";
    var _urlLangDefault = _baseUrlDefault + language + ".xml";
    var _urlDefault = _baseUrlDefault + ".xml";

        
        if (fileExists(_urlLang)) {
            _url = _urlLang;
        } else if (fileExists(_urlLangDefault)) {
            _url = _urlLangDefault;
        } else if (fileExists(_urlDefault)) {
            _url = _urlDefault;
        } else {
            console.log("gros pb aucun fichier de langue");
        }

        console.log("url translate : " + _url);
        return _url;


   /* } else {

        // language = "fr";
        //var _url = "/Resources/Default/translate.xml";

       
        if (fileExists(_urlLang)) {
            _url = _urlLang;
        } else if (fileExists(_urlLangDefault)) {
            _url = _urlLangDefault;
        } else if (fileExists(_urlDefault)) {
            _url = _urlDefault;
        } else {
            console.log("gros pb aucun fichier de langue")
        }

        console.log("url translate : " + _url);

        return _url;
    }*/


    return _url;
}


function IsIE(userAgent) {
    userAgent = userAgent || navigator.userAgent;
    return userAgent.indexOf("MSIE ") > -1 || userAgent.indexOf("Trident/") > -1;
}

function IsSafari() {

    var is_safari = navigator.userAgent.toLowerCase().indexOf('safari/') > -1;
    return is_safari;

}

function IsFirefox() {

    var is_firefox = navigator.userAgent.toLowerCase().indexOf('firefox') > -1;
    return is_firefox;

}


function GetNavigatorLanguage() {


    var language = window.navigator.userLanguage || window.navigator.language;

    if ($.browser.msie) {
        language = window.navigator.userLanguage.split('-')[0];
    }

    if ($.browser.safari) {
        language = window.navigator.language.split('-')[0];
    }


    return language;
}

/*
// Mets le datatable dans la bonne langue
function SetLangeDataTable(oTable, idTable) {

    var test = oTable;

    //  detruit le datatables sans les données pour remettre dans la bonne langue
    oTable.fnDestroy();
    oTable = null;
    oTable = $('#' + idTable).dataTable({ "iDisplayLength": -1, "oLanguage": $.parseJSON(GetLanguageDataTable()) });

  
}


function GetLanguageDataTable() {
    //window.location.origin+ "/assets/js/pages/datatableLanguages/es.js"
    //http://cdn.datatables.net/plug-ins/725b2a2115b/i18n/ ==> pour avoir les langues des datatables 

    var scriptLang;

    scriptLang = $.ajax({
        url: "../assets/js/pages/datatableLanguages/" + GetNavigatorLanguage() + ".js",
        async: false
    }).responseText;

    return scriptLang;

}

function GetUrlLanguageDataTable() {


    return "../assets/js/pages/datatableLanguages/" + GetNavigatorLanguage() + ".js";


}

function GetUrlLanguageDataTable() {


    var language = window.navigator.userLanguage || window.navigator.language;

    if ($.browser.msie) {
        language = window.navigator.userLanguage.split('-')[0];
    }

    if ($.browser.safari) {
        language = window.navigator.language.split('-')[0];
    }

    return "../assets/js/pages/datatableLanguages/" + language + ".js";


}

// Mets le datatable dans la bonne langue
function SetDescendingDataTable(idTable, idColumn, order) {
    //  detruit le datatables sans les donn?es pour remettre dans la bonne langue
    oTable.fnDestroy();
    oTable = null;
    oTable = $('#' + idTable).dataTable({  "iDisplayLength": -1, "oLanguage": $.parseJSON(GetLanguageDataTable()), "aaSorting": [[idColumn, order]] });
}*/

// Fonction d'initialisation pour surveiller les changements de langue
function InitLanguageWatcher() {
    console.log("=== InitLanguageWatcher ===");

    // Surveiller les changements sur les sélecteurs de langue
    $(document).on('change', '#selectLang, #selectLangues', function() {
        var newLang = $(this).val();
        console.log("Changement de langue détecté: " + newLang);

        if (newLang && newLang !== currentLanguage) {
            ReloadTranslations(newLang);
        }
    });

    // Surveiller les clics sur les boutons de validation de langue
    $(document).on('click', '#btnLanguagesValider', function() {
        console.log("Bouton de validation de langue cliqué");
        setTimeout(function() {
            var newLang = getSelectedLanguage();
            if (newLang && newLang !== currentLanguage) {
                ReloadTranslations(newLang);
            }
        }, 100); // Petit délai pour laisser le temps aux autres scripts de s'exécuter
    });
}

// Initialiser le surveillant de langue quand le document est prêt
$(document).ready(function() {
    InitLanguageWatcher();
});